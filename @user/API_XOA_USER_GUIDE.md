# API Xóa User - Hướng dẫn sử dụng

## Tổng quan

API xóa user đư<PERSON>c thiết kế để xóa user khỏi Keycloak và tất cả dữ liệu liên quan trong hệ thống. API bao gồm 2 endpoint chính:

1. **Kiểm tra cảnh báo**: Kiểm tra dữ liệu liên quan trước khi xóa
2. **Xóa user**: Thực hiện xóa user và dữ liệu liên quan

## Các bảng được kiểm tra

API sẽ kiểm tra các bảng sau trước khi xóa user:

### Bảng trong User Service:
- `user_schema.otp` - Mã OTP của user

### Bảng trong Portal Service (qua API call):
- `portal.employee` - Thông tin nhân viên
- `portal.partner` - Thông tin đối tác
- `portal.user_device` - Thiết bị của user
- `portal.user_partner` - <PERSON><PERSON> hệ user-partner

### Bảng trong Salary Advance Service:
- `salary_advance.transaction` - Giao dịch ứng lương
- `salary_advance.salary_advance_request` - Yêu cầu ứng lương
- `portal.salary_advance_limit` - Hạn mức ứng lương
- `salary_advance.notification_recipient` - Người nhận thông báo

## API Endpoints

### 1. Kiểm tra cảnh báo trước khi xóa

```http
GET /api/v1/user/{userId}/deletion-warnings
```

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Response:**
```json
{
    "httpStatusCode": 200,
    "description": "Success",
    "data": {
        "canDelete": false,
        "warnings": [
            "CẢNH BÁO: Xóa user sẽ ảnh hưởng đến dữ liệu liên quan:",
            "User có 2 mã OTP trong hệ thống",
            "User đã được liên kết với nhân viên: Nguyễn Văn A"
        ],
        "relatedData": [
            {
                "tableName": "user_schema.otp",
                "description": "Mã OTP của user",
                "count": 2
            },
            {
                "tableName": "portal.employee",
                "description": "Thông tin nhân viên",
                "count": 1
            }
        ]
    }
}
```

### 2. Xóa user (Normal Mode)

```http
DELETE /api/v1/user/{userId}?force=false
```

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Parameters:**
- `force` (optional): `false` (default) - Chế độ xóa thông thường

**Response thành công:**
```json
{
    "httpStatusCode": 200,
    "description": "Success",
    "data": {
        "success": true,
        "message": "Xóa user thành công",
        "data": null
    }
}
```

**Response lỗi:**
```json
{
    "httpStatusCode": 400,
    "description": "Bad Request",
    "data": {
        "success": false,
        "message": "Không thể xóa user: User đã được liên kết với nhân viên: Nguyễn Văn A",
        "data": null
    }
}
```

### 3. Xóa user (Force Mode)

```http
DELETE /api/v1/user/{userId}?force=true
```

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Parameters:**
- `force`: `true` - Chế độ force, xóa user ngay cả khi có liên kết

**Response thành công:**
```json
{
    "httpStatusCode": 200,
    "description": "Success",
    "data": {
        "success": true,
        "message": "Xóa user thành công (force mode): Đã xóa 2 OTP; Đã xóa liên kết với nhân viên: Nguyễn Văn A; Đã xóa user khỏi Keycloak",
        "data": null
    }
}
```

## Quy tắc xóa

### Normal Mode (force=false):
**❌ Không cho phép xóa khi:**
- User đã được liên kết với Employee (nhân viên)
- User đã được liên kết với Partner (đối tác)
- User có giao dịch ứng lương đang xử lý

**✅ Cho phép xóa và tự động xóa:**
- Mã OTP của user
- User khỏi Keycloak

### Force Mode (force=true):
**🔥 Xóa user ngay cả khi có liên kết:**
- Xóa tất cả OTP của user
- Xóa liên kết với Employee (chỉ xóa user_id, không xóa employee)
- Ghi log cảnh báo về Partner (không xóa partner vì quan trọng)
- Xóa user khỏi Keycloak

**⚠️ Lưu ý Force Mode:**
- Employee sẽ mất liên kết với user (user_id = null)
- Partner vẫn tồn tại, cần xử lý thủ công
- Không thể hoàn tác sau khi xóa

## Quy trình sử dụng

### Quy trình Normal Mode:
1. **Bước 1**: Gọi API kiểm tra cảnh báo để xem user có thể xóa được không
2. **Bước 2**: Nếu `canDelete = true`, gọi API xóa user với `force=false`
3. **Bước 3**: Kiểm tra response để xác nhận xóa thành công

### Quy trình Force Mode:
1. **Bước 1**: Gọi API kiểm tra cảnh báo để hiểu dữ liệu liên quan
2. **Bước 2**: Xác nhận muốn xóa user ngay cả khi có liên kết
3. **Bước 3**: Gọi API xóa user với `force=true`
4. **Bước 4**: Kiểm tra response và xử lý thủ công các dữ liệu còn lại (nếu có)

## Lưu ý quan trọng

- **Backup dữ liệu**: Luôn backup dữ liệu trước khi xóa user
- **Kiểm tra kỹ**: Sử dụng API kiểm tra cảnh báo trước khi xóa
- **Không thể hoàn tác**: Việc xóa user là vĩnh viễn và không thể hoàn tác
- **Quyền truy cập**: Chỉ admin mới có quyền xóa user

## Test với Postman

Import file `User_Delete_API.postman_collection.json` vào Postman để test API.

### Cấu hình variables:
- `base_url`: URL của user service (ví dụ: http://localhost:8080/user)
- `access_token`: Token xác thực
- `user_id`: ID của user cần xóa

## Troubleshooting

### Lỗi thường gặp:

1. **401 Unauthorized**: Kiểm tra access token
2. **403 Forbidden**: Kiểm tra quyền admin
3. **404 Not Found**: User không tồn tại
4. **400 Bad Request**: User có dữ liệu liên quan không thể xóa

### Log để debug:
- Kiểm tra log của user service
- Kiểm tra log của portal service
- Kiểm tra log của salary advance service
