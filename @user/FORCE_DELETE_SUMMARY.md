# Force Delete User - <PERSON><PERSON><PERSON> tắt tính năng

## Tổng quan

Đã thêm tính năng **Force Delete** cho phép xóa user ngay cả khi có liên kết với dữ liệu khác trong hệ thống.

## Thay đổi API

### Endpoint xóa user đã được cập nhật:

```http
DELETE /api/v1/user/{userId}?force={true|false}
```

**Parameters:**

- `force` (optional):
  - `false` (default): Chế độ xóa thông thường - không xóa nếu có liên kết
  - `true`: Chế độ force - xóa user ngay cả khi có liên kết

## So sánh 2 chế độ

### Normal Mode (force=false)

- ❌ **Từ chối xóa** nếu user có liên kết với Employee hoặc Partner
- ✅ **Chỉ xóa** khi user không có liên kết quan trọng
- 🔒 **An toàn** - tr<PERSON>h mất dữ liệu quan trọng

### Force Mode (force=true)

- 🔥 **Xóa user** ngay cả khi có liên kết
- 🔧 **Xử lý liên kết:**
  - Employee: Xóa `user_id` (employee vẫn tồn tại)
  - Partner: Ghi log cảnh báo (không xóa vì quan trọng)
  - OTP: Xóa hoàn toàn
- ⚠️ **Rủi ro cao** - cần cẩn thận khi sử dụng

## Ví dụ sử dụng

### 1. Kiểm tra cảnh báo trước

```bash
GET /api/v1/user/123/deletion-warnings
```

### 2. Thử xóa normal mode

```bash
DELETE /api/v1/user/123?force=false
# Response: "Không thể xóa user: User đã được liên kết với nhân viên"
```

### 3. Xóa force mode

```bash
DELETE /api/v1/user/123?force=true
# Response: "Xóa user thành công (force mode): Đã xóa 2 OTP; Đã xóa liên kết với nhân viên: Nguyễn Văn A; Đã xóa user khỏi Keycloak"
```

## Kết quả Force Delete

### Dữ liệu được xóa:

- ✅ User khỏi Keycloak
- ✅ Tất cả OTP của user
- ✅ Liên kết user_id trong Employee

### Dữ liệu được giữ lại:

- 🔄 Employee record (chỉ xóa user_id)
- ⚠️ Partner record (ghi log cảnh báo)
- 📝 Audit logs và transaction history

## Lưu ý quan trọng

### ⚠️ Cảnh báo Force Mode:

1. **Không thể hoàn tác** - User bị xóa vĩnh viễn khỏi Keycloak
2. **Employee mất liên kết** - Cần gán lại user mới nếu cần
3. **Partner cần xử lý thủ công** - Kiểm tra và xử lý riêng
4. **Chỉ admin** mới được sử dụng force mode

### 🔒 Bảo mật:

- Cần quyền admin để thực hiện force delete
- Ghi log đầy đủ mọi thao tác xóa
- Thông báo chi tiết về dữ liệu bị ảnh hưởng

## Test Cases

### Test Case 1: Normal Mode với liên kết

```
Input: DELETE /api/v1/user/123?force=false
Expected: 400 Bad Request - "Không thể xóa user: có liên kết"
```

### Test Case 2: Force Mode với liên kết

```
Input: DELETE /api/v1/user/123?force=true
Expected: 200 OK - "Xóa user thành công (force mode): [chi tiết]"
```

### Test Case 3: User không tồn tại

```
Input: DELETE /api/v1/user/999?force=true
Expected: 400 Bad Request - "User không tồn tại"
```

## Monitoring và Logging

### Logs được ghi:

- Thông tin user bị xóa
- Danh sách dữ liệu liên quan bị xóa/cập nhật
- Cảnh báo về dữ liệu cần xử lý thủ công
- Thời gian và người thực hiện

### Metrics cần theo dõi:

- Số lượng force delete per day
- Tỷ lệ normal vs force delete
- Số lượng employee mất liên kết
- Số lượng partner cần xử lý thủ công

## 🔍 Câu lệnh SQL kiểm tra

### Kiểm tra trước khi gọi API

#### 1. Kiểm tra OTP của user

```sql
-- Thay 'USER_ID_HERE' bằng userId thực tế
SELECT
    COUNT(*) as otp_count,
    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_otp,
    COUNT(CASE WHEN status = 'VERIFIED' THEN 1 END) as verified_otp,
    COUNT(CASE WHEN status = 'EXPIRED' THEN 1 END) as expired_otp
FROM user_schema.otp
WHERE user_id = 'USER_ID_HERE';

-- Chi tiết các OTP
SELECT
    id, code, type, status, contact_info,
    expiry_time, verification_attempts,
    created_at, delivery_method
FROM user_schema.otp
WHERE user_id = 'USER_ID_HERE'
ORDER BY created_at DESC;
```

#### 2. Kiểm tra Employee liên kết

```sql
-- Kiểm tra Employee
SELECT
    id, code, name, email, phone_number,
    partner_id, department_id, status,
    user_id, can_edit_account
FROM portal.employee
WHERE user_id = 'USER_ID_HERE'
AND is_soft_deleted = false;

-- Kiểm tra ứng lương của employee
SELECT sal.*, e.name as employee_name
FROM portal.salary_advance_limit sal
JOIN portal.employee e ON e.id::text = sal.employee_id
WHERE sal.employee_id = 'USER_ID_HERE'
ORDER BY sal.year DESC, sal.month DESC
LIMIT 5;
```

#### 3. Kiểm tra Partner liên kết

```sql
-- Kiểm tra Partner thông qua bảng user_partner
SELECT
    p.id, p.name, p.short_name, p.email, p.phone_number,
    p.status, p.tax_code, up.role
FROM portal.partner p
JOIN portal.user_partner up ON p.id = up.partner_id
WHERE up.user_id = 'USER_ID_HERE'
AND p.is_soft_deleted = false;
```

#### 4. Kiểm tra User Device

```sql
-- Kiểm tra thiết bị
SELECT
    id, device_token, device_type, device_info,
    status, created_at
FROM portal.user_device
WHERE user_id = 'USER_ID_HERE'
AND is_soft_deleted = false;
```

#### 5. Kiểm tra giao dịch ứng lương

```sql
-- Kiểm tra transaction
SELECT
    id, employee_name, advance_account as advance_amount,
    total_fee, actual_received_amount, status,
    created_at, description
FROM salary_advance.transaction
WHERE employee_id = 'USER_ID_HERE'
ORDER BY created_at DESC
LIMIT 10;
```

### Kiểm tra sau khi gọi API

#### 1. Xác nhận dữ liệu đã được xóa (PostgreSQL)

```sql
-- Kiểm tra OTP đã được xóa
SELECT COUNT(*) as remaining_otp_count
FROM user_schema.otp
WHERE user_id = 'USER_ID_HERE';

-- Kiểm tra Employee đã bị xóa user_id (nếu force=true)
SELECT
    id, code, name, user_id, status
FROM portal.employee
WHERE user_id = 'USER_ID_HERE'
AND is_soft_deleted = false;

-- Kiểm tra Partner vẫn tồn tại (không bị xóa)
SELECT
    p.id, p.name, p.status, up.role
FROM portal.partner p
JOIN portal.user_partner up ON p.id = up.partner_id
WHERE up.user_id = 'USER_ID_HERE'
AND p.is_soft_deleted = false;
```

### Script kiểm tra tổng hợp (PostgreSQL)

```sql
-- Script kiểm tra tổng hợp cho PostgreSQL
DO $$
DECLARE
    user_id_to_check VARCHAR(255) := 'USER_ID_HERE';  -- Thay bằng user ID thực tế
    otp_count INTEGER;
    employee_count INTEGER;
    partner_count INTEGER;
    device_count INTEGER;
    transaction_count INTEGER;
BEGIN
    -- Đếm các loại dữ liệu liên quan
    SELECT COUNT(*) INTO otp_count
    FROM user_schema.otp
    WHERE user_id = user_id_to_check;

    SELECT COUNT(*) INTO employee_count
    FROM portal.employee
    WHERE user_id = user_id_to_check AND is_soft_deleted = false;

    SELECT COUNT(*) INTO partner_count
    FROM portal.partner p
    JOIN portal.user_partner up ON p.id = up.partner_id
    WHERE up.user_id = user_id_to_check AND p.is_soft_deleted = false;

    SELECT COUNT(*) INTO device_count
    FROM portal.user_device
    WHERE user_id = user_id_to_check AND is_soft_deleted = false;

    SELECT COUNT(*) INTO transaction_count
    FROM salary_advance.transaction
    WHERE employee_id = user_id_to_check;

    -- In kết quả
    RAISE NOTICE 'User ID: %', user_id_to_check;
    RAISE NOTICE 'OTP count: %', otp_count;
    RAISE NOTICE 'Employee count: %', employee_count;
    RAISE NOTICE 'Partner count: %', partner_count;
    RAISE NOTICE 'Device count: %', device_count;
    RAISE NOTICE 'Transaction count: %', transaction_count;

    -- Đưa ra khuyến nghị
    IF employee_count > 0 OR partner_count > 0 THEN
        RAISE NOTICE 'WARNING: User has important relationships - consider using force=true';
    ELSE
        RAISE NOTICE 'OK: User can be safely deleted with force=false';
    END IF;
END $$;
```

### Hướng dẫn sử dụng SQL (PostgreSQL)

1. **Thay thế `USER_ID_HERE`** bằng userId thực tế cần kiểm tra
2. **Chạy trước API**: Để xem user có dữ liệu liên quan gì
3. **Chạy sau API**: Để xác nhận dữ liệu đã được xử lý đúng
4. **Script tổng hợp**: Sử dụng để có cái nhìn tổng quan nhanh về user
5. **Lưu ý PostgreSQL**:
   - Sử dụng `DO $$` block cho stored procedure
   - `RAISE NOTICE` để in thông tin debug
   - Partner liên kết với user thông qua bảng `portal.user_partner`
   - Không có column `keycloak_id` trực tiếp trong bảng `portal.partner`

### Kiểm tra sau khi gọi API

#### 1. Xác nhận dữ liệu đã được xóa

```sql
-- Kiểm tra OTP đã được xóa
SELECT COUNT(*) as remaining_otp_count
FROM user_schema.otp
WHERE user_id = 'USER_ID_HERE';

-- Kiểm tra Employee đã bị xóa user_id (force=true)
SELECT
    id, code, name, user_id, status
FROM portal.employee
WHERE id::text = 'USER_ID_HERE'
   OR user_id = 'USER_ID_HERE'
AND is_soft_deleted = false;

-- Kiểm tra Partner vẫn tồn tại (không bị xóa)
SELECT
    id, name, status
FROM portal.partner
WHERE keycloak_id = 'USER_ID_HERE'
AND is_soft_deleted = false;
```

### Script kiểm tra tổng hợp

```sql
-- Script kiểm tra tổng hợp trước khi xóa user (CẬP NHẬT 2025)
DO $$
DECLARE
    user_id_to_check VARCHAR(255) := '8c1515b6-0217-4156-9678-13691353fa28';  -- Thay bằng user ID thực tế

    -- User Service
    otp_count INTEGER;

    -- Portal Service
    employee_count INTEGER;
    partner_count INTEGER;
    device_count INTEGER;
    salary_limit_count INTEGER;
    salary_limit_aud_count INTEGER;

    -- Salary Advance Service
    transaction_count INTEGER;
    transaction_aud_count INTEGER;
    request_count INTEGER;
    request_aud_count INTEGER;

    total_count INTEGER;
BEGIN
    -- =====================================================
    -- 1. USER SERVICE - Đếm dữ liệu
    -- =====================================================
    SELECT COUNT(*) INTO otp_count
    FROM user_schema.otp
    WHERE user_id = user_id_to_check;

    -- =====================================================
    -- 2. PORTAL SERVICE - Đếm dữ liệu
    -- =====================================================
    SELECT COUNT(*) INTO employee_count
    FROM portal.employee
    WHERE user_id = user_id_to_check AND is_soft_deleted = false;

    SELECT COUNT(*) INTO partner_count
    FROM portal.partner p
    JOIN portal.user_partner up ON p.id = up.partner_id
    WHERE up.user_id = user_id_to_check AND p.is_soft_deleted = false;

    SELECT COUNT(*) INTO device_count
    FROM portal.user_device
    WHERE user_id = user_id_to_check AND status = 'ACTIVE';

    -- BẢNG MỚI: salary_advance_limit
    SELECT COUNT(*) INTO salary_limit_count
    FROM portal.salary_advance_limit
    WHERE employee_id = user_id_to_check;

    -- BẢNG MỚI: salary_advance_limit_aud
    SELECT COUNT(*) INTO salary_limit_aud_count
    FROM portal.salary_advance_limit_aud
    WHERE employee_id = user_id_to_check;

    -- =====================================================
    -- 3. SALARY ADVANCE SERVICE - Đếm dữ liệu
    -- =====================================================
    SELECT COUNT(*) INTO transaction_count
    FROM salary_advance.transaction
    WHERE employee_id = user_id_to_check;

    -- BẢNG MỚI: transaction_aud
    SELECT COUNT(*) INTO transaction_aud_count
    FROM salary_advance.transaction_aud
    WHERE employee_id = user_id_to_check;

    -- BẢNG MỚI: salary_advance_request
    SELECT COUNT(*) INTO request_count
    FROM salary_advance.salary_advance_request
    WHERE employee_id = user_id_to_check;

    -- BẢNG MỚI: salary_advance_request_aud
    SELECT COUNT(*) INTO request_aud_count
    FROM salary_advance.salary_advance_request_aud
    WHERE employee_id = user_id_to_check;

    -- Tính tổng
    total_count := otp_count + employee_count + partner_count + device_count +
                   salary_limit_count + salary_limit_aud_count +
                   transaction_count + transaction_aud_count +
                   request_count + request_aud_count;

    -- =====================================================
    -- 4. IN KẾT QUẢ CHI TIẾT
    -- =====================================================
    RAISE NOTICE '========================================';
    RAISE NOTICE '=== KIỂM TRA USER: % ===', user_id_to_check;
    RAISE NOTICE '========================================';

    RAISE NOTICE '📋 USER SERVICE:';
    RAISE NOTICE '  └── OTP records: %', otp_count;

    RAISE NOTICE '🏢 PORTAL SERVICE:';
    RAISE NOTICE '  ├── Employee records: %', employee_count;
    RAISE NOTICE '  ├── Partner relationships: %', partner_count;
    RAISE NOTICE '  ├── User devices: %', device_count;
    RAISE NOTICE '  ├── Salary advance limits: %', salary_limit_count;
    RAISE NOTICE '  └── Salary limit audit logs: %', salary_limit_aud_count;

    RAISE NOTICE '💰 SALARY ADVANCE SERVICE:';
    RAISE NOTICE '  ├── Transactions: %', transaction_count;
    RAISE NOTICE '  ├── Transaction audit logs: %', transaction_aud_count;
    RAISE NOTICE '  ├── Salary advance requests: %', request_count;
    RAISE NOTICE '  └── Request audit logs: %', request_aud_count;

    RAISE NOTICE '========================================';

    -- =====================================================
    -- 5. PHÂN TÍCH VÀ KHUYẾN NGHỊ
    -- =====================================================
    IF employee_count > 0 OR partner_count > 0 THEN
        RAISE NOTICE '⚠️  WARNING: User has critical business relationships!';
        RAISE NOTICE '💡 Recommendation: Use force=true to delete';

        IF employee_count > 0 THEN
            RAISE NOTICE '👤 IMPACT: Employee record will lose user link';
        END IF;

        IF partner_count > 0 THEN
            RAISE NOTICE '🏢 IMPACT: Partner relationship needs manual review';
        END IF;
    ELSE
        RAISE NOTICE '✅ OK: User can be safely deleted with force=false';
    END IF;

    -- Cảnh báo về dữ liệu tài chính
    IF transaction_count > 0 OR request_count > 0 OR salary_limit_count > 0 THEN
        RAISE NOTICE '💸 FINANCIAL DATA WARNING:';
        RAISE NOTICE '   This user has salary advance history!';
        RAISE NOTICE '   Consider data retention policies before deletion.';
    END IF;

    -- =====================================================
    -- 6. TỔNG KẾT
    -- =====================================================
    RAISE NOTICE '========================================';
    RAISE NOTICE '📊 SUMMARY:';
    RAISE NOTICE '  └── Total related records: %', total_count;

    IF total_count = 0 THEN
        RAISE NOTICE '🎉 User has no related data - safe to delete!';
    ELSIF total_count < 10 THEN
        RAISE NOTICE '⚡ Low impact deletion';
    ELSIF total_count < 50 THEN
        RAISE NOTICE '⚠️  Medium impact deletion - review carefully';
    ELSE
        RAISE NOTICE '🚨 High impact deletion - requires thorough review!';
    END IF;

    RAISE NOTICE '========================================';
END $$;
```

### Hướng dẫn sử dụng SQL

1. **Thay thế user ID** bằng userId thực tế cần kiểm tra (dòng 336)
2. **Chạy trước API**: Để xem user có dữ liệu liên quan gì
3. **Chạy sau API**: Để xác nhận dữ liệu đã được xử lý đúng
4. **Script mới bao gồm**: Tất cả 10 bảng liên quan (6 bảng mới được thêm)

## ⚠️ **CÁC BẢNG THIẾU TRONG HÀM XÓA USER**

### 🚨 **Phát hiện mới (2025):**

Hàm `deleteUser` hiện tại **CHƯA XỬ LÝ** 6 bảng quan trọng:

#### **Portal Service (2 bảng):**

- `portal.salary_advance_limit` - Hạn mức ứng lương
- `portal.salary_advance_limit_aud` - Audit log hạn mức

#### **Salary Advance Service (4 bảng):**

- `salary_advance.transaction` - Giao dịch ứng lương
- `salary_advance.transaction_aud` - Audit log giao dịch
- `salary_advance.salary_advance_request` - Yêu cầu ứng lương
- `salary_advance.salary_advance_request_aud` - Audit log yêu cầu

### 📋 **Trạng thái xử lý:**

#### ✅ **Đã xử lý:**

- `user_schema.otp` - Xóa trong User service
- `portal.user_device` - Xóa qua deleteUserDevices()
- `portal.user_partner` - Xóa qua deleteUserPartner()
- `portal.employee` - Chỉ xóa user_id (soft delete)

#### ❌ **Chưa xử lý:**

- `portal.salary_advance_limit` - **TODO**
- `portal.salary_advance_limit_aud` - **TODO**
- `salary_advance.transaction` - **TODO**
- `salary_advance.transaction_aud` - **TODO**
- `salary_advance.salary_advance_request` - **TODO**
- `salary_advance.salary_advance_request_aud` - **TODO**

### 🔧 **Script xóa thủ công (nếu cần):**

```sql
-- CẢNH BÁO: CHỈ CHẠY KHI THỰC SỰ CẦN XÓA USER
-- Thay thế USER_ID_HERE bằng ID thực tế

-- Xóa dữ liệu Portal Service
DELETE FROM portal.salary_advance_limit WHERE employee_id = 'USER_ID_HERE';
DELETE FROM portal.salary_advance_limit_aud WHERE employee_id = 'USER_ID_HERE';

-- Xóa dữ liệu Salary Advance Service
DELETE FROM salary_advance.transaction WHERE employee_id = 'USER_ID_HERE';
DELETE FROM salary_advance.transaction_aud WHERE employee_id = 'USER_ID_HERE';
DELETE FROM salary_advance.salary_advance_request WHERE employee_id = 'USER_ID_HERE';
DELETE FROM salary_advance.salary_advance_request_aud WHERE employee_id = 'USER_ID_HERE';
```

### 💡 **Khuyến nghị:**

1. **Ngắn hạn**: Sử dụng script SQL thủ công để xóa dữ liệu
2. **Dài hạn**: Bổ sung APIs và logic vào hàm deleteUser
3. **Kiểm tra**: Luôn chạy script kiểm tra trước và sau khi xóa user
4. **Script tổng hợp**: Sử dụng để có cái nhìn tổng quan nhanh về user
