# Force Delete User - <PERSON><PERSON><PERSON> tắt tính năng

## Tổng quan

Đã thêm tính năng **Force Delete** cho phép xóa user ngay cả khi có liên kết với dữ liệu khác trong hệ thống.

## Thay đổi API

### Endpoint xóa user đã được cập nhật:

```http
DELETE /api/v1/user/{userId}?force={true|false}
```

**Parameters:**
- `force` (optional): 
  - `false` (default): Chế độ xóa thông thường - không xóa nếu có liên kết
  - `true`: Chế độ force - xóa user ngay cả khi có liên kết

## So sánh 2 chế độ

### Normal Mode (force=false)
- ❌ **Từ chối xóa** nếu user có liên kết với Employee hoặc Partner
- ✅ **Chỉ xóa** khi user không có liên kết quan trọng
- 🔒 **An toàn** - tr<PERSON>h mất dữ liệu quan trọng

### Force Mode (force=true)
- 🔥 **Xóa user** ngay cả khi có liên kết
- 🔧 **Xử lý liên kết:**
  - Employee: Xóa `user_id` (employee vẫn tồn tại)
  - Partner: Ghi log cảnh báo (không xóa vì quan trọng)
  - OTP: Xóa hoàn toàn
- ⚠️ **Rủi ro cao** - cần cẩn thận khi sử dụng

## Ví dụ sử dụng

### 1. Kiểm tra cảnh báo trước
```bash
GET /api/v1/user/123/deletion-warnings
```

### 2. Thử xóa normal mode
```bash
DELETE /api/v1/user/123?force=false
# Response: "Không thể xóa user: User đã được liên kết với nhân viên"
```

### 3. Xóa force mode
```bash
DELETE /api/v1/user/123?force=true
# Response: "Xóa user thành công (force mode): Đã xóa 2 OTP; Đã xóa liên kết với nhân viên: Nguyễn Văn A; Đã xóa user khỏi Keycloak"
```

## Kết quả Force Delete

### Dữ liệu được xóa:
- ✅ User khỏi Keycloak
- ✅ Tất cả OTP của user
- ✅ Liên kết user_id trong Employee

### Dữ liệu được giữ lại:
- 🔄 Employee record (chỉ xóa user_id)
- ⚠️ Partner record (ghi log cảnh báo)
- 📝 Audit logs và transaction history

## Lưu ý quan trọng

### ⚠️ Cảnh báo Force Mode:
1. **Không thể hoàn tác** - User bị xóa vĩnh viễn khỏi Keycloak
2. **Employee mất liên kết** - Cần gán lại user mới nếu cần
3. **Partner cần xử lý thủ công** - Kiểm tra và xử lý riêng
4. **Chỉ admin** mới được sử dụng force mode

### 🔒 Bảo mật:
- Cần quyền admin để thực hiện force delete
- Ghi log đầy đủ mọi thao tác xóa
- Thông báo chi tiết về dữ liệu bị ảnh hưởng

## Test Cases

### Test Case 1: Normal Mode với liên kết
```
Input: DELETE /api/v1/user/123?force=false
Expected: 400 Bad Request - "Không thể xóa user: có liên kết"
```

### Test Case 2: Force Mode với liên kết
```
Input: DELETE /api/v1/user/123?force=true
Expected: 200 OK - "Xóa user thành công (force mode): [chi tiết]"
```

### Test Case 3: User không tồn tại
```
Input: DELETE /api/v1/user/999?force=true
Expected: 400 Bad Request - "User không tồn tại"
```

## Monitoring và Logging

### Logs được ghi:
- Thông tin user bị xóa
- Danh sách dữ liệu liên quan bị xóa/cập nhật
- Cảnh báo về dữ liệu cần xử lý thủ công
- Thời gian và người thực hiện

### Metrics cần theo dõi:
- Số lượng force delete per day
- Tỷ lệ normal vs force delete
- Số lượng employee mất liên kết
- Số lượng partner cần xử lý thủ công
