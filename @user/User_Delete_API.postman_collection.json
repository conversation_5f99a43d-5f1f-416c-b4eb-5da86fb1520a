{"info": {"_postman_id": "user-delete-api-collection", "name": "User Delete API Collection", "description": "Collection để test API xóa user và kiểm tra cảnh báo", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. <PERSON><PERSON><PERSON> tra cảnh báo tr<PERSON><PERSON><PERSON> khi xóa user", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}/deletion-warnings", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}", "deletion-warnings"]}, "description": "API để kiểm tra các cảnh báo và dữ liệu liên quan trước khi xóa user"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}/deletion-warnings", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}", "deletion-warnings"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"httpStatusCode\": 200,\n    \"description\": \"Success\",\n    \"data\": {\n        \"canDelete\": false,\n        \"warnings\": [\n            \"CẢNH BÁO: Xóa user sẽ ảnh hưởng đến dữ liệu liên quan:\",\n            \"User có 2 mã OTP trong hệ thống\",\n            \"User đã được liên kết với nhân viên: <PERSON>uyễn Văn <PERSON>\"\n        ],\n        \"relatedData\": [\n            {\n                \"tableName\": \"user_schema.otp\",\n                \"description\": \"Mã OTP của user\",\n                \"count\": 2\n            },\n            {\n                \"tableName\": \"portal.employee\",\n                \"description\": \"Thông tin nhân viên\",\n                \"count\": 1\n            }\n        ]\n    }\n}"}]}, {"name": "2. <PERSON><PERSON><PERSON> user (normal mode)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}?force=false", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}"], "query": [{"key": "force", "value": "false", "description": "Chế độ xóa thông thường - không xóa nếu có liên kết"}]}, "description": "API để xóa user khỏi Keycloak và dữ liệu liên quan (chế độ thông thường)"}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"httpStatusCode\": 200,\n    \"description\": \"Success\",\n    \"data\": {\n        \"success\": true,\n        \"message\": \"Xóa user thành công\",\n        \"data\": null\n    }\n}"}, {"name": "Error Response - Cannot Delete", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"httpStatusCode\": 400,\n    \"description\": \"Bad Request\",\n    \"data\": {\n        \"success\": false,\n        \"message\": \"<PERSON>hông thể xóa user: User đ<PERSON> đư<PERSON><PERSON> liên kết với nhân viên: <PERSON><PERSON><PERSON><PERSON>n <PERSON>\",\n        \"data\": null\n    }\n}"}]}, {"name": "3. <PERSON><PERSON><PERSON> user (force mode)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}?force=true", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}"], "query": [{"key": "force", "value": "true", "description": "Chế độ force - xóa user ngay cả khi có liên kết"}]}, "description": "API để xóa user khỏi Keycloak và dữ liệu liên quan (chế độ force - xóa ngay cả khi có liên kết)"}, "response": [{"name": "Success Response - Force Mode", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user/{{user_id}}?force=true", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "{{user_id}}"], "query": [{"key": "force", "value": "true"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"httpStatusCode\": 200,\n    \"description\": \"Success\",\n    \"data\": {\n        \"success\": true,\n        \"message\": \"Xóa user thành công (force mode): Đã xóa 2 OTP; Đã xóa liên kết với nhân viên: <PERSON><PERSON><PERSON><PERSON>; Đã xóa user khỏi Keycloak\",\n        \"data\": null\n    }\n}"}]}, {"name": "4. <PERSON><PERSON><PERSON>nh s<PERSON>ch users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/user?page=0&size=10&role=true", "host": ["{{base_url}}"], "path": ["api", "v1", "user"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "role", "value": "true"}]}, "description": "API để lấy danh sách users để chọn user cần xóa"}}], "variable": [{"key": "base_url", "value": "http://localhost:8080/user", "type": "string"}, {"key": "access_token", "value": "your_access_token_here", "type": "string"}, {"key": "user_id", "value": "user_id_to_delete", "type": "string"}]}