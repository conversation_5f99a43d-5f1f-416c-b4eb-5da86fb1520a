# Development Environment Configuration
server.port=8088

# PVComBank API Configuration - DEV
pvcom.bank.client.url=https://awsapi-uat.pvcombank.com.vn/
pvcom.bank.grant-type=client_credentials
pvcom.bank.client-id=TGCORP
pvcom.bank.client-secret=GoAA9qtX3pNY4V4adjvo2dYeP0pA11BY

# Keycloak Configuration - DEV
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs
keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn

# Database Configuration - DEV
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

# Logging Configuration - DEV
logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.cloud.openfeign.Feign=DEBUG
