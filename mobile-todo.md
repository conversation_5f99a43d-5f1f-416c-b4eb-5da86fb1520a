# TODO: <PERSON><PERSON><PERSON> hợp xác thực OTP cho đăng ký tài khoản

## Tổng quan
Cập nhật ứng dụng mobile để hỗ trợ xác thực OTP khi đăng ký tài khoản mới. <PERSON><PERSON> khi đăng ký, người dùng sẽ cần xác thực OTP trước khi có thể đăng nhập.

## Các nhiệm vụ cần thực hiện

### 1. Cập nhật luồng đăng ký
- [ ] Giữ nguyên màn hình đăng ký hiện tại (nhập số điện thoại và mật khẩu)
- [ ] Sau khi gọi API đăng ký thành công, chuyển sang màn hình nhập OTP thay vì màn hình đăng nhập
- [ ] Thêm thông báo cho người dùng biết cần xác thực OTP để kích hoạt tài khoản

### 2. Tạo màn hình nhập OTP
- [ ] Thiết kế màn hình nhập OTP với 6 ô nhập liệu
- [ ] Hiển thị số điện thoại đã đăng ký (đã che một phần) để người dùng biết OTP được gửi đến đâu
- [ ] Thêm đồng hồ đếm ngược thời gian hiệu lực của OTP (5 phút)
- [ ] Thêm nút "Gửi lại OTP" (chỉ hiển thị sau khi đồng hồ đếm ngược kết thúc)
- [ ] Thêm nút "Xác nhận" để gửi OTP đã nhập

### 3. Tích hợp API
- [ ] Thêm gọi API gửi OTP (`/api/v1/otp/generate`) ngay sau khi đăng ký thành công
- [ ] Tích hợp API xác thực OTP (`/api/v1/otp/verify`)
- [ ] Tích hợp API gửi lại OTP khi người dùng nhấn "Gửi lại OTP"
- [ ] Cập nhật xử lý lỗi đăng nhập khi tài khoản chưa xác thực

### 4. Xử lý các trường hợp đặc biệt
- [ ] Xử lý trường hợp người dùng thoát ứng dụng trước khi xác thực OTP
- [ ] Xử lý trường hợp OTP hết hạn
- [ ] Xử lý trường hợp người dùng nhập sai OTP quá 3 lần
- [ ] Xử lý trường hợp người dùng không nhận được OTP

### 5. Cập nhật luồng đăng nhập
- [ ] Cập nhật xử lý lỗi đăng nhập khi tài khoản chưa xác thực
- [ ] Khi nhận được lỗi "Tài khoản chưa được xác thực", hiển thị màn hình nhập OTP
- [ ] Tự động gọi API gửi OTP khi chuyển sang màn hình nhập OTP từ màn hình đăng nhập

### 6. Kiểm thử
- [ ] Kiểm thử đăng ký tài khoản mới và xác thực OTP
- [ ] Kiểm thử gửi lại OTP
- [ ] Kiểm thử đăng nhập với tài khoản chưa xác thực
- [ ] Kiểm thử đăng nhập với tài khoản đã xác thực
- [ ] Kiểm thử nhập sai OTP
- [ ] Kiểm thử OTP hết hạn

### 7. Cập nhật tài liệu
- [ ] Cập nhật tài liệu hướng dẫn sử dụng
- [ ] Cập nhật thông tin phiên bản
- [ ] Chuẩn bị thông báo cho người dùng về tính năng mới

## Thông tin API

### 1. Đăng ký tài khoản
```
POST /api/v1/user/register
```
**Request Body:**
```json
{
  "username": "0987654321",
  "password": "123456"
}
```

### 2. Gửi OTP
```
POST /api/v1/otp/generate
```
**Request Body:**
```json
{
  "type": "REGISTRATION",
  "contactInfo": "0987654321",
  "deliveryMethod": "ZNS"
}
```

### 3. Xác thực OTP
```
POST /api/v1/otp/verify
```
**Request Body:**
```json
{
  "code": "123456",
  "type": "REGISTRATION",
  "contactInfo": "0987654321"
}
```

### 4. Đăng nhập
```
POST /api/v1/user/login
```
**Request Body:**
```json
{
  "username": "0987654321",
  "password": "123456"
}
```

## Lưu ý quan trọng
- OTP có hiệu lực trong 5 phút
- Người dùng có tối đa 3 lần thử xác thực OTP
- Tài khoản sẽ không thể đăng nhập cho đến khi xác thực OTP thành công
- Sử dụng các API OTP chung thay vì các API riêng cho đăng ký

## Thời hạn
- Ngày bắt đầu: [Ngày]
- Ngày hoàn thành dự kiến: [Ngày]
- Ngày phát hành: [Ngày]
