# Tài liệu hướng dẫn tích hợp OTP

## Giới thiệu

Tài liệu này mô tả chi tiết về hệ thống OTP (One-Time Password) và cách tích hợp vào các luồng nghiệp vụ khác nhau trong ứng dụng. Hệ thống OTP được thiết kế để cung cấp lớp bảo mật bổ sung cho các hoạt động quan trọng như đăng ký tài kho<PERSON>n, đ<PERSON><PERSON> nhập, xá<PERSON> thực giao dịch, và đặt lại mật khẩu.

## Các loại OTP

Hệ thống hỗ trợ các loại OTP sau:

1. **REGISTRATION**: Dùng để xác thực khi đăng ký tài khoản mới
2. **LOGIN_VERIFICATION**: Dùng để xác thực khi đăng nhập
3. **TRANSACTION_VERIFICATION**: Dùng để xác thực giao dịch
4. **PASSWORD_RESET**: Dùng để xác thực khi đặt lại mật khẩu

## Phương thức gửi OTP

Hiện tại hệ thống hỗ trợ gửi OTP qua:

- **ZNS** (Zalo Notification Service): Gửi OTP qua tin nhắn Zalo

## Các API OTP

### 1. Tạo OTP

```
POST /api/v1/otp/generate
```

**Request Body:**
```json
{
  "type": "REGISTRATION",
  "contactInfo": "0987654321",
  "deliveryMethod": "ZNS",
  "additionalData": "Thông tin bổ sung (tùy chọn)",
  "templateId": "ID template ZNS (tùy chọn)"
}
```

**Response:**
```json
{
  "code": 200,
  "message": "Mã OTP đã được gửi thành công",
  "data": {
    "otpId": 12345,
    "maskedContactInfo": "098*****21",
    "expiryTime": "2023-06-15T10:30:00",
    "remainingTimeInSeconds": 300
  }
}
```

### 2. Xác thực OTP

```
POST /api/v1/otp/verify
```

**Request Body:**
```json
{
  "code": "123456",
  "type": "REGISTRATION",
  "contactInfo": "0987654321"
}
```

**Response:**
```json
{
  "code": 200,
  "message": "Xác thực OTP thành công",
  "data": {
    "success": true,
    "type": "REGISTRATION",
    "message": "Xác thực OTP thành công",
    "verificationToken": null
  }
}
```

## Tích hợp OTP vào luồng đăng ký tài khoản

### Luồng đăng ký với xác thực OTP

1. Người dùng đăng ký tài khoản qua API `/api/v1/user/register`
2. Tài khoản được tạo trong hệ thống với trạng thái chưa kích hoạt (`enabled=false`)
3. Ứng dụng gọi API `/api/v1/otp/generate` để gửi OTP đến số điện thoại đã đăng ký
4. Người dùng nhập OTP và ứng dụng gọi API `/api/v1/otp/verify` để xác thực
5. Nếu xác thực thành công, tài khoản được tự động kích hoạt (`enabled=true`)
6. Người dùng có thể đăng nhập bình thường

### Xử lý các trường hợp đặc biệt

#### Người dùng không nhận được OTP
- Cung cấp nút "Gửi lại OTP" trên giao diện
- Khi người dùng nhấn nút, gọi lại API `/api/v1/otp/generate`

#### OTP hết hạn
- OTP có hiệu lực trong 5 phút
- Nếu OTP hết hạn, thông báo cho người dùng và cung cấp tùy chọn gửi lại OTP

#### Nhập sai OTP
- Người dùng có tối đa 3 lần thử xác thực OTP
- Nếu vượt quá số lần thử, OTP sẽ bị vô hiệu hóa và người dùng cần yêu cầu OTP mới

#### Đăng nhập khi tài khoản chưa xác thực
- Nếu người dùng cố gắng đăng nhập khi tài khoản chưa được xác thực, hệ thống sẽ trả về lỗi
- Ứng dụng nên hiển thị màn hình nhập OTP và gọi API gửi OTP

## Tích hợp OTP vào luồng đặt lại mật khẩu

### Luồng đặt lại mật khẩu với xác thực OTP

1. Người dùng yêu cầu đặt lại mật khẩu
2. Ứng dụng gọi API `/api/v1/otp/generate` với `type=PASSWORD_RESET`
3. Người dùng nhập OTP và ứng dụng gọi API `/api/v1/otp/verify`
4. Nếu xác thực thành công, ứng dụng cho phép người dùng đặt mật khẩu mới
5. Ứng dụng gọi API đặt lại mật khẩu với token xác thực từ bước 3

## Tích hợp OTP vào luồng xác thực giao dịch

### Luồng xác thực giao dịch với OTP

1. Người dùng tạo giao dịch
2. Ứng dụng gọi API `/api/v1/otp/generate` với `type=TRANSACTION_VERIFICATION`
3. Người dùng nhập OTP và ứng dụng gọi API `/api/v1/otp/verify`
4. Nếu xác thực thành công, ứng dụng hoàn tất giao dịch

## Cấu trúc dữ liệu OTP

OTP được lưu trữ trong cơ sở dữ liệu với các thông tin sau:

- **id**: ID của OTP
- **code**: Mã OTP (6 chữ số)
- **expiryTime**: Thời gian hết hạn
- **verificationAttempts**: Số lần thử xác thực
- **status**: Trạng thái (PENDING, VERIFIED, EXPIRED, INVALIDATED)
- **type**: Loại OTP (REGISTRATION, LOGIN_VERIFICATION, TRANSACTION_VERIFICATION, PASSWORD_RESET)
- **userId**: ID của người dùng
- **contactInfo**: Thông tin liên hệ (số điện thoại)
- **deliveryMethod**: Phương thức gửi (ZNS)
- **additionalData**: Dữ liệu bổ sung

## Các thông số cấu hình

- **otp.expiry.minutes**: Thời gian hiệu lực của OTP (mặc định: 5 phút)
- **otp.max.attempts**: Số lần thử xác thực tối đa (mặc định: 3 lần)
- **otp.length**: Độ dài của mã OTP (mặc định: 6 chữ số)

## Bảo mật

- OTP được tạo ngẫu nhiên bằng thuật toán an toàn (SecureRandom)
- Mỗi OTP chỉ có hiệu lực trong thời gian giới hạn
- Số lần thử xác thực bị giới hạn để ngăn chặn tấn công brute force
- OTP cũ sẽ bị vô hiệu hóa khi tạo OTP mới cùng loại

## Xử lý lỗi

### Lỗi khi tạo OTP

- **400 Bad Request**: Thông tin không hợp lệ
- **404 Not Found**: Không tìm thấy người dùng

### Lỗi khi xác thực OTP

- **400 Bad Request**: OTP không hợp lệ hoặc đã hết hạn
- **400 Bad Request**: Đã vượt quá số lần thử xác thực
- **404 Not Found**: Không tìm thấy OTP