package com.salaryadvance.commonlibrary.config;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class ApplicationContextHolder implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    public static <T> T getBean(Class<T> clazz){ return applicationContext.getBean(clazz);}

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        synchronized (this){
            if(ApplicationContextHolder.applicationContext == null){
                ApplicationContextHolder.applicationContext = applicationContext;
            }
        }
    }
}
