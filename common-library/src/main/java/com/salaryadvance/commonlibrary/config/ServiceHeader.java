package com.salaryadvance.commonlibrary.config;

import com.salaryadvance.commonlibrary.constants.HttpKey;
import com.salaryadvance.commonlibrary.utils.JsonUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.servlet.HandlerMapping;

import java.util.Objects;
import java.util.UUID;

@Setter
@Getter
@Data
public class ServiceHeader {
    private String contextPath;
    private String uri;
    private String httpMethod;
    private String clientMessageId;
    private String traceId;
    private Long timestamp;
    private String srcAppIp;
    private int srcAppPort;
    private String destAppIp;
    private int destAppPort;
    private String authorization;
    private int httpCode;

    public ServiceHeader() {
    }

    public ServiceHeader(String contextPath, String uri, String httpMethod, String clientMessageId, String traceId,
            Long timestamp, String srcAppIp, int srcAppPort, String destAppIp, int destAppPort, String authorization,
            int httpCode) {
        this.contextPath = contextPath;
        this.uri = uri;
        this.httpMethod = httpMethod;
        this.clientMessageId = clientMessageId;
        this.traceId = traceId;
        this.timestamp = timestamp;
        this.srcAppIp = srcAppIp;
        this.srcAppPort = srcAppPort;
        this.destAppIp = destAppIp;
        this.destAppPort = destAppPort;
        this.authorization = authorization;
        this.httpCode = httpCode;
    }

    public static ServiceHeader of(HttpServletRequest request) {
        String clientMessageId = request.getHeader(HttpKey.CLIENT_MESSAGE_ID);
        if (clientMessageId == null)
            clientMessageId = UUID.randomUUID().toString();
        String traceId = request.getHeader(HttpKey.TRACE_ID);
        if (traceId == null)
            traceId = UUID.randomUUID().toString();
        Object auth = request.getHeader(HttpKey.AUTHORIZATION);
        String authorization = Objects.nonNull(auth) ? "<<Not intent to log>>" : null;
        Object uriObj = request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String uri = uriObj != null ? (String) uriObj : "/";
        ServiceHeader header = new ServiceHeader();
        header.setUri(uri);
        header.setSrcAppIp(request.getRemoteAddr());
        header.setDestAppIp(request.getLocalAddr());
        header.setSrcAppPort(request.getRemotePort());
        header.setDestAppPort(request.getLocalPort());
        header.setHttpMethod(request.getMethod());
        header.setClientMessageId(clientMessageId);
        header.setTraceId(traceId);
        header.setAuthorization(authorization);
        return header;
    }

    public String toString() {
        return JsonUtils.toJson(this);
    }

}
