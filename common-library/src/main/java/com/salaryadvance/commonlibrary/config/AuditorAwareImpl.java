package com.salaryadvance.commonlibrary.config;

import com.salaryadvance.commonlibrary.utils.TokenUtils;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("auditorAwareImpl")
public class AuditorAwareImpl implements AuditorAware<String> {

    @Override
    @SuppressWarnings("all")
    public Optional<String> getCurrentAuditor() {
        String username = TokenUtils.getUsername();
        // N<PERSON>u không có username, trả về "anonymous" thay vì null
        return Optional.ofNullable(username != null ? username : "anonymous");
    }
}
