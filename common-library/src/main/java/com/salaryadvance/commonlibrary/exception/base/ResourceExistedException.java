package com.salaryadvance.commonlibrary.exception.base;

public class ResourceExistedException extends ApplicationException {

    public ResourceExistedException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.RESOURCE_EXISTED, ErrorSeverity.ERROR, context);
    }

    public ResourceExistedException(String message, Object context) {
        super(message, StatusCode.RESOURCE_EXISTED, ErrorSeverity.ERROR, context);
    }

}
