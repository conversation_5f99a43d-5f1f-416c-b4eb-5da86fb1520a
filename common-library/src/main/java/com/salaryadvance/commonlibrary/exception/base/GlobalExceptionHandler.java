package com.salaryadvance.commonlibrary.exception.base;

import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.rest.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler{

    @ExceptionHandler(ResourceExistedException.class)
    public ResponseEntity<Response<Object>> handleResourceExistedException(ResourceExistedException e) {
        log.error(e.getMessage(), e);
        return Response.of(e.getErrorCode().getHttpStatus(), Map.of("message", e.getMessage()));
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Response<Object>> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.error(e.getMessage(), e);
        return Response.of(e.getErrorCode().getHttpStatus(), Map.of("message", e.getMessage()));
    }

    @ExceptionHandler(ExternalServiceException.class)
    public ResponseEntity<Response<Object>> handleExternalServiceException(ExternalServiceException e) {
        log.error(e.getMessage(), e);
        return Response.of(e.getErrorCode().getHttpStatus(), Map.of("message", e.getMessage(), "details", e.getContext()));
    }

    @ExceptionHandler(DataValidationException.class)
    public ResponseEntity<Response<Object>> handleDataValidationException(DataValidationException e) {
        log.error(e.getMessage(), e);
        return Response.of(e.getErrorCode().getHttpStatus(), Map.of("message", e.getContext()));
    }

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<Response<Object>> badRequestException(BadRequestException e) {
        log.error(e.getMessage(), e);
        return Response.of(HttpStatus.BAD_REQUEST, Map.of("message", e.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Response<Object>> handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
        Map<String, String> invalidField = new HashMap<>();
        if (ex != null) {
            List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
            for (FieldError fieldError : fieldErrors) {
                invalidField.put(fieldError.getField(), fieldError.getDefaultMessage());
            }
        } else {
            invalidField.put("message", ex.getLocalizedMessage());
        }
        String message = invalidField.entrySet().iterator().next().getValue();
        return Response.of(HttpStatus.BAD_REQUEST, Map.of("message", message));
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<Response<Object>> httpMessageNotReadableException(HttpMessageNotReadableException ex) {
        log.error(ex.getMessage(), ex);
        return Response.of(HttpStatus.BAD_REQUEST, Map.of("message", ex.getMessage()));
    }

    @ExceptionHandler(FileUploadException.class)
    public ResponseEntity<Response<Object>> handleFileUploadException(FileUploadException e) {
        log.error(e.getMessage(), e);
        return Response.of(e.getErrorCode().getHttpStatus(), Map.of("message", e.getMessage()));
    }
}
