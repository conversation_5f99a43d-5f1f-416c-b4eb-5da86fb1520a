package com.salaryadvance.commonlibrary.exception.base;

public class BusinessException extends ApplicationException {

    public BusinessException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.BUSINESS_RULE_VIOLATION, ErrorSeverity.ERROR, context);
    }

    public BusinessException(String message, Object context) {
        super(message, StatusCode.BUSINESS_RULE_VIOLATION, ErrorSeverity.ERROR, context);
    }

}