package com.salaryadvance.commonlibrary.exception.base;

public class ForbiddenException extends ApplicationException {

    protected ForbiddenException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.FORBIDDEN, ErrorSeverity.ERROR, context);
    }

    protected ForbiddenException(String message, Object context) {
        super(message, StatusCode.FORBIDDEN, ErrorSeverity.ERROR, context);
    }

}
