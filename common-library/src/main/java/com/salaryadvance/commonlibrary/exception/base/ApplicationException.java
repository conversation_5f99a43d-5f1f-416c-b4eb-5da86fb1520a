package com.salaryadvance.commonlibrary.exception.base;

import com.salaryadvance.commonlibrary.constants.HttpKey;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.logging.log4j.ThreadContext;

@Data
@EqualsAndHashCode(callSuper = false)
public abstract class ApplicationException extends RuntimeException {

    private String clientMessageId;
    private StatusCode errorCode;
    private ErrorSeverity errorSeverity;
    private Object context;

    protected ApplicationException(String message, Throwable cause, StatusCode errorCode, ErrorSeverity errorSeverity, Object context) {
        super(message, cause);
        this.clientMessageId = ThreadContext.get(HttpKey.CLIENT_MESSAGE_ID);
        this.errorCode = errorCode;
        this.errorSeverity = errorSeverity;
        this.context = context;
    }

    protected ApplicationException(String message, StatusCode errorCode, ErrorSeverity errorSeverity, Object context) {
        super(message);
        this.clientMessageId = ThreadContext.get(HttpKey.CLIENT_MESSAGE_ID);
        this.errorCode = errorCode;
        this.errorSeverity = errorSeverity;
        this.context = context;
    }
}
