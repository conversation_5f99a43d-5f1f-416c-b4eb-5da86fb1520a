package com.salaryadvance.commonlibrary.exception.base;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum StatusCode {

    SUCCESS("00", HttpStatus.OK),
    UNAUTHORIZED("10", HttpStatus.UNAUTHORIZED),
    FORBIDDEN("20", HttpStatus.FORBIDDEN),
    RESOURCE_NOT_FOUND("30", HttpStatus.NOT_FOUND),
    RESOURCE_EXISTED("40", HttpStatus.CONFLICT),
    DATA_VALIDATION_ERROR("50", HttpStatus.BAD_REQUEST),
    BUSINESS_RULE_VIOLATION("60", HttpStatus.BAD_REQUEST),
    SYSTEM_ERROR("70", HttpStatus.INTERNAL_SERVER_ERROR);

    private final String code;
    private final HttpStatus httpStatus;

    StatusCode(String code, HttpStatus httpStatus) {
         this.code = code;
         this.httpStatus = httpStatus;
    }
}
