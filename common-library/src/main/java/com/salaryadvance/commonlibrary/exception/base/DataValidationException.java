package com.salaryadvance.commonlibrary.exception.base;

public class DataValidationException extends ApplicationException {

    public DataValidationException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.DATA_VALIDATION_ERROR, ErrorSeverity.WARNING, context);
    }

    public DataValidationException(String message, Object context) {
        super(message, StatusCode.DATA_VALIDATION_ERROR, ErrorSeverity.WARNING, context);
    }

}
