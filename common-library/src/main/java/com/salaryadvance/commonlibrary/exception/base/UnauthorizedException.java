package com.salaryadvance.commonlibrary.exception.base;

public class UnauthorizedException extends ApplicationException {

    public UnauthorizedException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.UNAUTHORIZED, ErrorSeverity.ERROR, context);
    }

    public UnauthorizedException(String message, Object context) {
        super(message, StatusCode.UNAUTHORIZED, ErrorSeverity.ERROR, context);
    }

}
