package com.salaryadvance.commonlibrary.exception.base;

public class ResourceNotFoundException extends ApplicationException {

    public ResourceNotFoundException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.RESOURCE_NOT_FOUND, ErrorSeverity.ERROR, context);
    }

    public ResourceNotFoundException(String message, Object context) {
        super(message, StatusCode.RESOURCE_NOT_FOUND, ErrorSeverity.ERROR, context);
    }

    public ResourceNotFoundException(String message) {
        super(message, StatusCode.RESOURCE_NOT_FOUND, ErrorSeverity.ERROR, null);
    }
}
