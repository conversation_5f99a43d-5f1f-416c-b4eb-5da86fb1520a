package com.salaryadvance.commonlibrary.exception.base;

public class SystemException extends ApplicationException {

    public SystemException(String message, Throwable cause, Object context) {
        super(message, cause, StatusCode.SYSTEM_ERROR, ErrorSeverity.CRITICAL, context);
    }

    public SystemException(String message, Object context) {
        super(message, StatusCode.SYSTEM_ERROR, ErrorSeverity.CRITICAL, context);
    }

}
