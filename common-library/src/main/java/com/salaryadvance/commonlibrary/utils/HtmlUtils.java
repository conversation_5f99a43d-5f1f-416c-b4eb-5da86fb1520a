package com.salaryadvance.commonlibrary.utils;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * Utility class for HTML operations
 * <AUTHOR>
 */
public class HtmlUtils {

    private HtmlUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Converts HTML content to plain text
     * - Preserves paragraph and line breaks
     * - Removes HTML tags while keeping their content
     * - Handles common HTML entities
     *
     * @param html HTML content to convert
     * @return Plain text representation of the HTML content
     */
    public static String htmlToText(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }

        // Parse HTML using Jsoup
        Document document = Jsoup.parse(html);
        
        // Handle paragraphs, line breaks, and lists specially
        Elements paragraphs = document.select("p, h1, h2, h3, h4, h5, h6");
        for (Element paragraph : paragraphs) {
            paragraph.after("\n\n");
        }
        
        Elements lineBreaks = document.select("br");
        for (Element br : lineBreaks) {
            br.after("\n");
        }
        
        Elements listItems = document.select("li");
        for (Element item : listItems) {
            item.before("• ");
            item.after("\n");
        }
        
        // Get text content, which automatically removes all HTML tags
        String text = document.text();
        
        // Clean up extra whitespace
        text = text.replaceAll("\\s+", " ").trim();
        
        return text;
    }
}
