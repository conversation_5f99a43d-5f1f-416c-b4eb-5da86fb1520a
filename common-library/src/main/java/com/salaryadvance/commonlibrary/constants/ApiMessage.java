package com.salaryadvance.commonlibrary.constants;

public class ApiMessage {
    public static final String NOT_FOUND = "%s not found!";
    public static final String CREATED = "%s is created!";
    public static final String UPDATED = "%s is updated!";
    public static final String DELETED = "%s is deleted!";
    public static final String APPROVED = "%s is approved!";
    public static final String REJECTED = "%s is rejected!";
    public static final String LOGGED_IN = "%s is logged in!";

    public static String notFound(String resource) {
        return String.format(NOT_FOUND, resource);
    }

    public static String created(String resource) {
        return String.format(CREATED, resource);
    }

    public static String updated(String resource) {
        return String.format(UPDATED, resource);
    }

    public static String deleted(String resource) {
        return String.format(DELETED, resource);
    }

    public static String approved(String resource) {
        return String.format(APPROVED, resource);
    }

    public static String rejected(String resource) {
        return String.format(REJECTED, resource);
    }

    public static String loggedIn() {
        return String.format(LOG<PERSON>D_IN, "User");
    }
}
