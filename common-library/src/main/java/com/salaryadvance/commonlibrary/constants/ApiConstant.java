package com.salaryadvance.commonlibrary.constants;

public final class ApiConstant {
    public static final String WAREHOUSE_URL = "/backoffice/warehouses";
    public static final String STOCK_HISTORY_URL = "/backoffice/stocks/histories";
    
    public static final String CODE_200 = "200";
    public static final String OK = "Ok";
    
    public static final String CODE_201 = "201";
    public static final String CREATED = "Created";
    
    public static final String CODE_204 = "204";
    public static final String NO_CONTENT = "No content";
    
    public static final String CODE_400 = "400";
    public static final String BAD_REQUEST = "Bad request";
    
    public static final String CODE_401 = "401";
    public static final String UNAUTHORIZED = "Unauthorized";
    
    public static final String CODE_403 = "403";
    public static final String FORBIDDEN = "Forbidden";
    public static final String ACCESS_DENIED = "ACCESS_DENIED";
    
    public static final String CODE_404 = "404";
    public static final String NOT_FOUND = "Not found";

    public static final String CODE_500 = "500";
    public static final String INTERNAL_SERVER_ERROR = "Internal server error";
    

    private ApiConstant() {
        //Add constructor
    }
}
