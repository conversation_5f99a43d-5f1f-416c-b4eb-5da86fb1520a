package com.salaryadvance.commonlibrary.excel;

import com.salaryadvance.commonlibrary.utils.DateUtils;
import com.salaryadvance.commonlibrary.utils.ExcelUtils;
import com.salaryadvance.commonlibrary.utils.TypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public abstract class AbstractExcelImportHandler<T> implements ExcelImportHandler<T> {

    @Override
    public Map<String, Integer> getTitles(Sheet sheet) {
        int firstRowIndex = getConfiguration().getFirstRowIndex();
        int firstColumnIndex = getConfiguration().getFirstColumnIndex();
        int lastColumnIndex = getConfiguration().getLastColumnIndex();
        if (firstRowIndex == 0) {
            log.info("Table has no title");
            return null;
        }
        Row titleRow = sheet.getRow(firstRowIndex - 1);
        Map<String, Integer> titles = new LinkedHashMap<>();
        for (int i = firstColumnIndex; i <= lastColumnIndex; i++) {
            Cell cell = titleRow.getCell(i);
            if (Objects.nonNull(cell)) {
                titles.put(ExcelUtils.getValueAsString(cell), i);
            }
        }
        return titles;
    }

    @Override
    public void validateFile(MultipartFile file) {
        String contentType = file.getContentType();
        boolean isValid = Objects.nonNull(contentType) &&
                (contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                 contentType.equals("application/vnd.ms-excel"));
        if (!isValid) throw new IllegalArgumentException("Invalid file type");
    }

    @Override
    public ExcelData<T> processFile(Map<String, Object> input, MultipartFile file, Class<T> clazz, Resource template) throws IOException, ReflectiveOperationException {
        validateFile(file);
        ExcelData<T> excelData = new ExcelData<>();
        boolean isPreview = Boolean.TRUE.equals(input.get("preview")); // Kiểm tra nếu preview được bật

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            Workbook resultWorkbook = (Objects.nonNull(template)) ? new XSSFWorkbook(template.getInputStream()) : null;
            Sheet resultSheet = (Objects.nonNull(resultWorkbook)) ? resultWorkbook.getSheetAt(0) : null;
            int firstRowIndex = getConfiguration().getFirstRowIndex();
            int lastRowIndex = ExcelUtils.getLastRowActualIndex(sheet, firstRowIndex, getConfiguration().getFirstColumnIndex(), getConfiguration().getLastColumnIndex());
            if (lastRowIndex < firstRowIndex) {
                throw new IllegalArgumentException("Excel file is empty");
            }
            int lastColumn = getConfiguration().getLastColumnIndex();
            if (Objects.nonNull(resultWorkbook)) {
                Cell resourceCell = resultSheet.getRow(firstRowIndex - 1).createCell(getConfiguration().getLastColumnIndex() + 1);
                resourceCell.setCellValue(ExcelUtils.IMPORT_RESULT);
                ExcelUtils.copyStyle(resultSheet, firstRowIndex - 1, firstRowIndex - 1, lastColumn + 1, lastColumn);
            }
            Map<String, Integer> titles = getTitles(sheet);
            Object extraData = getExtraData(sheet);
            excelData.setTitles(titles);
            excelData.setExtraData(extraData);
            Map<Integer, String> errorData = new LinkedHashMap<>();
            Map<Integer, T> data = new LinkedHashMap<>();
            for (int i = firstRowIndex; i <= lastRowIndex; i++) {
                Row row = sheet.getRow(i);
                // Sửa lỗi: bỏ qua dòng null, nhưng không break mà continue để duyệt hết các dòng
                if (Objects.isNull(row)) continue;
                Row resultRow = resultSheet.createRow(i);
                ExcelUtils.cloneRow(row, resultRow, resultWorkbook);
                Cell resultCell = resultRow.createCell(lastColumn + 1);
                ExcelUtils.copyStyle(resultSheet, i, i, lastColumn + 1, lastColumn);
                T record = mapRow(input, row, clazz);
                if (!isPreview) {
                    boolean isValid = validateRow(input, row, resultCell, i, errorData, resultSheet, resultWorkbook);
                    if (!isValid) continue;
                    processFileInternal(input, record, row, resultCell, i, resultSheet, resultWorkbook, data, errorData);
                } else {
                    data.put(i, record); // Nếu preview, chỉ thêm vào danh sách JSON
                }
            }
            excelData.setErrorData(errorData);
            excelData.setData(data);
            if (!isPreview && Objects.nonNull(resultWorkbook)) {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                resultWorkbook.write(bos);
                excelData.setReturnContent(bos.toByteArray());
            }
            return excelData;
        }
    }

    @Override
    public T mapRow(Map<String, Object> input, Row row, Class<T> clazz) throws ReflectiveOperationException {
        Constructor<T> defaultConstructor = clazz.getConstructor();
        defaultConstructor.setAccessible(true);
        T record = defaultConstructor.newInstance();
        Map<String, Integer> mappingMetadata = getConfiguration().getMappingMetadata();

        for (Map.Entry<String, Integer> entry : mappingMetadata.entrySet()) {
            String fieldName = entry.getKey();
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Cell cell = row.getCell(entry.getValue());
            if (TypeUtils.isDate(field.getType())) {
                Date date = ExcelUtils.getValueAsDate(cell);
                assert date != null;
                Instant instant = date.toInstant();
                field.set(record, DateUtils.fromInstantSystemZone(instant, field.getType()));
            } else {
                String s = ExcelUtils.getValueAsString(cell);
                Object val = TypeUtils.castValueAs(s, field.getType());
                field.set(record, val);
            }
        }
        mapRowAdditionalData(input, row.getSheet(), record);
        return record;
    }
}
