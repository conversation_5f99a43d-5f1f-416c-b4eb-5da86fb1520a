package com.salaryadvance.commonlibrary.excel;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * Common operation for basic import file of list resource process
 * <AUTHOR> <PERSON>o
 * */
public interface ExcelImportHandler<T> {

    /**
     * Configuration for table scan range, mapping metadata
     * */
    MappingConfig getConfiguration();

    /**
     * Validate excel file eg: content type
     * */
    void validateFile(MultipartFile file);

    /**
     * Get table title
     * */
    Map<String, Integer> getTitles(Sheet sheet);

    /**
     * Process Excel file
     * */
    ExcelData<T> processFile(
            Map<String, Object> input,
            MultipartFile file,
            Class<T> clazz,
            Resource resource) throws IOException, ReflectiveOperationException;

    /**
     * Process after successfully mapping row to record T
     * */
    void processFileInternal(
            Map<String, Object> input, T record,
            Row row, Cell resultCell, int index, Sheet resultSheet, Workbook resultWorkbook,
            Map<Integer, T> data, Map<Integer, String> errorData);

    boolean validateRow(Map<String, Object> input, Row row, Cell resultCell, Integer index, Map<Integer, String> errorData, Sheet resultSheet, Workbook resultWorkbook);

    T mapRow(Map<String, Object> input, Row row, Class<T> clazz) throws ReflectiveOperationException;

    Object getExtraData(Sheet sheet);

    void mapRowAdditionalData(Map<String, Object> input, Sheet sheet, T record);

}
