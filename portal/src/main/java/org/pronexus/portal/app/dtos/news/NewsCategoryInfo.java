package org.pronexus.portal.app.dtos.news;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pronexus.portal.domain.entities.type.NewsCategoryStatus;

/**
 * DTO cho thông tin category trong News response
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewsCategoryInfo {
    private Long id;
    private String name;
    private String slug;
    private String description;
    private Long parentId;
    private String parentName;
    private Integer displayOrder;
    private NewsCategoryStatus status;
}
