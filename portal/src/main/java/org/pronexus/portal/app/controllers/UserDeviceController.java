package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.rest.Response;
import lombok.RequiredArgsConstructor;
import org.pronexus.portal.app.dtos.CreateUserDeviceDto;
import org.pronexus.portal.app.dtos.UpdateUserDeviceDto;
import org.pronexus.portal.app.response.UserDeviceRes;
import org.pronexus.portal.domain.services.core.UserDeviceService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/user-device")
@RequiredArgsConstructor
public class UserDeviceController {
    private final UserDeviceService userDeviceService;

    /**
     * Tạo mới thiết bị người dùng
     */
    @PostMapping
    public ResponseEntity<Response<Boolean>> createUserDevice(@RequestBody CreateUserDeviceDto dto) {
        return Response.success(userDeviceService.createUserDevice(dto));
    }

    /**
     * Cập nhật thiết bị người dùng
     */
    @PutMapping("/{id}")
    public ResponseEntity<Response<Boolean>> updateUserDevice(@PathVariable Integer id,
            @RequestBody UpdateUserDeviceDto dto) {
        return Response.success(userDeviceService.updateUserDevice(id, dto));
    }

    /**
     * Xóa thiết bị người dùng theo ID
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Response<Boolean>> delUserDevice(@PathVariable Integer id) {
        return Response.success(userDeviceService.delUserDevice(id));
    }

    /**
     * Lấy danh sách thiết bị của user
     */
    @GetMapping
    public ResponseEntity<Response<List<UserDeviceRes>>> getUserDevices(@RequestParam String userId) {
        return Response.success(userDeviceService.getUserDevice(userId));
    }

    /**
     * Xóa tất cả thiết bị của user (cho User service gọi)
     */
    @DeleteMapping("/user/{userId}")
    public ResponseEntity<Response<Boolean>> deleteAllUserDevices(@PathVariable String userId) {
        return Response.success(userDeviceService.deleteAllUserDevices(userId));
    }
}
