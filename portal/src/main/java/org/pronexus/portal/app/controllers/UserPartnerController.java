package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.rest.Response;
import lombok.RequiredArgsConstructor;
import org.pronexus.portal.domain.services.core.UserDeviceService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user-partner")
@RequiredArgsConstructor
public class UserPartnerController {
    private final UserDeviceService userDeviceService;

    /**
     * <PERSON><PERSON>a liên kết user-partner (cho User service gọi)
     */
    @DeleteMapping("/user/{userId}")
    public ResponseEntity<Response<Boolean>> deleteUserPartner(@PathVariable String userId) {
        return Response.success(userDeviceService.deleteUserPartner(userId));
    }
}
