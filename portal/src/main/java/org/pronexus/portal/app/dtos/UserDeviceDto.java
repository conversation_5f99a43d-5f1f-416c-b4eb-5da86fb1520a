package org.pronexus.portal.app.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO cho thông tin thiết bị của user
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDeviceDto {
    private Integer id;
    private String userId;
    private String deviceToken;
    private String deviceType;
    private Object deviceInfo; // JSON object
    private String status;
    private Long createdAt;
    private String createdBy;
    private boolean isSoftDeleted;
    private Long updatedAt;
    private String updatedBy;
}
