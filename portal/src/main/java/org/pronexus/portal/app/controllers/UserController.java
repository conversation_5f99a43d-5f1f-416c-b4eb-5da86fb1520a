package org.pronexus.portal.app.controllers;

import com.salaryadvance.commonlibrary.rest.Response;
import lombok.RequiredArgsConstructor;
import org.pronexus.portal.app.dtos.CreateUserDeviceDto;
import org.pronexus.portal.app.dtos.UpdateUserDeviceDto;
import org.pronexus.portal.domain.services.core.UserDeviceService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/user")
@RequiredArgsConstructor
public class UserController {
    private final UserDeviceService userDeviceService;

    /**
     * @since: 1/7/2025 9:00 PM
     * @description: them sua xoa, thiet bi cua user
     **/
    @PostMapping("device")
    public ResponseEntity<Response<Boolean>> createUserDevice(@RequestBody CreateUserDeviceDto dto) {
        return Response.success(userDeviceService.createUserDevice(dto));
    }

    @PutMapping("device/{id}")
    public ResponseEntity<Response<Boolean>> updateUserDevice(@PathVariable Integer id,
            @RequestBody UpdateUserDeviceDto dto) {
        return Response.success(userDeviceService.updateUserDevice(id, dto));
    }

    @DeleteMapping("device/{id}")
    public ResponseEntity<Response<Boolean>> delUserDevice(@PathVariable Integer id) {
        return Response.success(userDeviceService.delUserDevice(id));
    }

    @DeleteMapping("device/{userId}")
    public ResponseEntity<Response<Boolean>> deleteAllUserDevices(@PathVariable String userId) {
        return Response.success(userDeviceService.deleteAllUserDevices(userId));
    }

    @DeleteMapping("partner/{userId}")
    public ResponseEntity<Response<Boolean>> deleteUserPartner(@PathVariable String userId) {
        return Response.success(userDeviceService.deleteUserPartner(userId));
    }
}
