package org.pronexus.portal.domain.mappers;

import org.mapstruct.Mapper;
import org.pronexus.portal.app.response.UserDeviceRes;
import org.pronexus.portal.domain.entities.UserDevice;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserDeviceMapper {
    List<UserDeviceRes> toUserDeviceRes(List<UserDevice> userDevices);

    UserDeviceRes toUserDeviceRes(UserDevice userDevice);
}
