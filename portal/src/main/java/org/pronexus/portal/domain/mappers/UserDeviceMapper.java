package org.pronexus.portal.domain.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.pronexus.portal.app.dtos.UserDeviceDto;
import org.pronexus.portal.app.response.UserDeviceRes;
import org.pronexus.portal.domain.entities.UserDevice;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserDeviceMapper {
    List<UserDeviceRes> toUserDeviceRes(List<UserDevice> userDevices);

    UserDeviceRes toUserDeviceRes(UserDevice userDevice);

    @Mapping(target = "deviceType", expression = "java(userDevice.getDeviceType() != null ? userDevice.getDeviceType().name() : null)")
    @Mapping(target = "status", expression = "java(userDevice.getStatus() != null ? userDevice.getStatus().name() : null)")
    UserDeviceDto toUserDeviceDto(UserDevice userDevice);

    List<UserDeviceDto> toUserDeviceDto(List<UserDevice> userDevices);
}
