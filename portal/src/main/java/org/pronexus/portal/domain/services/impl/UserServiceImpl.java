package org.pronexus.portal.domain.services.impl;

import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.utils.JsonUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.extern.log4j.Log4j2;
import org.pronexus.portal.app.dtos.CreateUserDeviceDto;
import org.pronexus.portal.app.dtos.UpdateUserDeviceDto;
import org.pronexus.portal.app.response.UserDeviceRes;
import org.pronexus.portal.domain.entities.UserDevice;
import org.pronexus.portal.domain.entities.UserPartner;
import org.pronexus.portal.domain.entities.type.UserDeviceStatus;
import org.pronexus.portal.domain.mappers.UserDeviceMapper;
import org.pronexus.portal.domain.repositories.UserDeviceRepository;
import org.pronexus.portal.domain.repositories.UserPartnerRepository;
import org.pronexus.portal.domain.services.core.UserDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation cho việc quản lý người dùng
 * Xử lý các thao tác liên quan đến người dùng, thiết bị người dùng
 * và các chức năng liên quan đến người dùng
 */
@Service
@Log4j2
public class UserServiceImpl implements UserDeviceService {
    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private UserPartnerRepository userPartnerRepository;

    @Autowired
    private UserDeviceMapper userDeviceMapper;

    /**
     * Tạo mới thiết bị người dùng
     *
     * @param dto DTO chứa thông tin tạo thiết bị
     * @return Boolean xác nhận tạo thành công
     */
    @Override
    @Transactional
    public Boolean createUserDevice(CreateUserDeviceDto dto) {
        String userId = TokenUtils.getUserId();
        log.info("createUserDevice userId : {} , dto: {}", userId, dto);

        this.userDeviceRepository.deleteAllByUserId(userId);

        // Tạo token mới
        UserDevice userDevice = new UserDevice();
        userDevice.createFrom(userId, dto.getDeviceToken(), dto.getDeviceType(), JsonUtils.toJson(dto.getDeviceInfo()));
        userDeviceRepository.save(userDevice);
        log.info("createUserDevice userDevice: {}", userDevice);
        return true;
    }

    /**
     * Cập nhật thông tin thiết bị người dùng
     *
     * @param id  ID của thiết bị
     * @param dto DTO chứa thông tin cập nhật
     * @return Boolean xác nhận cập nhật thành công
     */
    @Override
    public Boolean updateUserDevice(Integer id, UpdateUserDeviceDto dto) {
        String userId = TokenUtils.getUserId();
        log.info("updateUserDevice id: {}, userId : {} , dto: {}", id, userId, dto);
        UserDevice userDevice = userDeviceRepository.findUserDeviceByUserIdAndIdAndStatus(userId, id,
                UserDeviceStatus.ACTIVE);
        if (userDevice == null) {
            throw new BadRequestException("Không tìm thấy thông tin thiết bị");
        }
        userDevice.updateFrom(JsonUtils.toJson(dto.getDeviceInfo()));
        userDeviceRepository.save(userDevice);
        log.info("updateUserDevice userDevice: {}", userDevice);
        return true;
    }

    /**
     * Xóa thiết bị người dùng
     *
     * @param id ID của thiết bị
     * @return Boolean xác nhận xóa thành công
     */
    @Override
    public Boolean delUserDevice(Integer id) {
        String userId = TokenUtils.getUserId();
        log.info("delUserDevice id: {}, userId : {}", id, userId);
        UserDevice userDevice = userDeviceRepository.findUserDeviceByUserIdAndIdAndStatus(userId, id,
                UserDeviceStatus.ACTIVE);
        if (userDevice == null) {
            throw new BadRequestException("Không tìm thấy thông tin thiết bị");
        }
        userDevice.setStatus(UserDeviceStatus.DELETED);
        userDeviceRepository.save(userDevice);
        log.info("updateUserDevice userDevice: {}", userDevice);
        return true;
    }

    /**
     * Xóa tất cả thiết bị của người dùng
     *
     * @param userId ID của người dùng
     * @return Boolean xác nhận xóa thành công
     */
    @Override
    public Boolean deleteAllUserDevices(String userId) {
        List<UserDevice> userDevices = userDeviceRepository.findUserDevicesByUserIdAndStatus(String.valueOf(userId),
                UserDeviceStatus.ACTIVE);
        for (UserDevice userDevice : userDevices) {
            userDevice.setStatus(UserDeviceStatus.DELETED);
            userDeviceRepository.save(userDevice);
        }
        return true;
    }

    /**
     * Lấy danh sách thiết bị của người dùng
     *
     * @param userId ID của người dùng
     * @return Danh sách thông tin thiết bị
     */
    @Override
    public List<UserDeviceRes> getUserDevice(String userId) {
        log.info("getUserDevice by: {}", userId);
        List<UserDevice> userDevices;
        if (userId != null) {
            userDevices = userDeviceRepository.findUserDevicesByUserIdAndStatus(userId, UserDeviceStatus.ACTIVE);
        } else {
            userDevices = userDeviceRepository.findAllByStatus(UserDeviceStatus.ACTIVE);
        }
        List<UserDeviceRes> userDeviceRes = userDeviceMapper.toUserDeviceRes(userDevices);
        log.info("getUserDevice response: {}", userDeviceRes);
        return userDeviceRes;
    }

    /**
     * Lấy danh sách token thiết bị của nhiều người dùng
     *
     * @param userIds Danh sách ID người dùng
     * @return Danh sách token thiết bị
     */
    @Override
    public List<String> getDeviceTokensByUserIds(List<String> userIds) {
        log.info("getDeviceTokensByUserIds userIds: {}", userIds);

        if (userIds == null || userIds.isEmpty()) {
            log.warn("getDeviceTokensByUserIds: userIds is null or empty");
            return List.of();
        }

        // Lấy tất cả thiết bị của các người dùng trong danh sách
        List<UserDevice> allUserDevices = new java.util.ArrayList<>();

        for (String userId : userIds) {
            List<UserDevice> userDevices = userDeviceRepository.findUserDevicesByUserIdAndStatus(userId,
                    UserDeviceStatus.ACTIVE);
            if (userDevices != null && !userDevices.isEmpty()) {
                allUserDevices.addAll(userDevices);
            }
        }

        // Lọc và trả về danh sách token
        List<String> tokens = allUserDevices.stream()
                .map(UserDevice::getDeviceToken)
                .filter(token -> token != null && !token.isEmpty())
                .distinct() // Loại bỏ các token trùng lặp
                .toList();

        log.info("getDeviceTokensByUserIds found {} tokens for {} users", tokens.size(), userIds.size());
        return tokens;
    }

    /**
     * Xóa liên kết user-partner theo userId
     *
     * @param userId ID của user
     * @return Boolean xác nhận xóa thành công
     */
    @Override
    public Boolean deleteUserPartner(String userId) {
        try {
            UserPartner userPartner = userPartnerRepository.findUserPartnerByUserId(userId);
            if (userPartner != null) {
                userPartnerRepository.delete(userPartner);
                log.info("Deleted user_partner relationship for user: {}", userId);
                return true;
            } else {
                log.info("No user_partner relationship found for user: {}", userId);
                return true; // Không có gì để xóa cũng coi như thành công
            }
        } catch (Exception e) {
            log.error("Error deleting user_partner for user {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }
}
