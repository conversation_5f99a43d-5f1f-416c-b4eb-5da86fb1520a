-- <PERSON><PERSON><PERSON> để drop và recreate bảng OTP với naming convention đúng

-- 1. Drop bảng cũ (nếu có)
DROP TABLE IF EXISTS user_schema.otp CASCADE;

-- 2. T<PERSON>o lại bảng với naming convention snake_case
-- Hibernate sẽ tự động tạo bảng với các column names đúng khi restart application
-- V<PERSON><PERSON> CamelCaseToUnderscoresNamingStrategy:
-- contactInfo -> contact_info
-- userId -> user_id
-- expiryTime -> expiry_time
-- verificationAttempts -> verification_attempts
-- deliveryMethod -> delivery_method
-- additionalData -> additional_data

-- Hoặc có thể tạo manual:
CREATE TABLE user_schema.otp (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(6) NOT NULL,
    expiry_time TIMESTAMP NOT NULL,
    verification_attempts INTEGER,
    status VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    contact_info VARCHAR(255) NOT NULL,
    delivery_method VARCHAR(50) NOT NULL,
    additional_data TEXT,
    created_at BIGINT,
    created_by VARCHAR(255),
    is_soft_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    updated_at BIGINT,
    updated_by VARCHAR(255)
);

-- 3. Tạo indexes (nếu cần)
CREATE INDEX idx_otp_user_id_type ON user_schema.otp(user_id, type);
CREATE INDEX idx_otp_code_user_id ON user_schema.otp(code, user_id);
CREATE INDEX idx_otp_status ON user_schema.otp(status);
CREATE INDEX idx_otp_expiry_time ON user_schema.otp(expiry_time);

-- 4. Comment cho bảng
COMMENT ON TABLE user_schema.otp IS 'Bảng lưu trữ thông tin OTP cho xác thực người dùng';
COMMENT ON COLUMN user_schema.otp.code IS 'Mã OTP 6 chữ số';
COMMENT ON COLUMN user_schema.otp.contact_info IS 'Thông tin liên hệ (email hoặc số điện thoại)';
COMMENT ON COLUMN user_schema.otp.user_id IS 'ID của người dùng';
COMMENT ON COLUMN user_schema.otp.expiry_time IS 'Thời gian hết hạn của OTP';
COMMENT ON COLUMN user_schema.otp.verification_attempts IS 'Số lần đã thử xác thực';
COMMENT ON COLUMN user_schema.otp.status IS 'Trạng thái OTP (PENDING, VERIFIED, EXPIRED, INVALIDATED)';
COMMENT ON COLUMN user_schema.otp.type IS 'Loại OTP (REGISTRATION, LOGIN_VERIFICATION, TRANSACTION_VERIFICATION, PASSWORD_RESET)';
COMMENT ON COLUMN user_schema.otp.delivery_method IS 'Phương thức gửi OTP (ZNS)';
COMMENT ON COLUMN user_schema.otp.additional_data IS 'Dữ liệu bổ sung liên quan đến OTP';
