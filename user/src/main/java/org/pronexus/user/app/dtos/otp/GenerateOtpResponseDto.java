package org.pronexus.user.app.dtos.otp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpType;

import java.time.LocalDateTime;

/**
 * DTO cho response khi tạo OTP
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateOtpResponseDto {
    
    /**
     * ID của OTP
     */
    private Long id;
    
    /**
     * Thời gian hết hạn của OTP
     */
    private LocalDateTime expiryTime;
    
    /**
     * Loại OTP
     */
    private OtpType type;
    
    /**
     * Thông tin liên hệ đã được che một phần
     */
    private String maskedContactInfo;
    
    /**
     * Phương thức gửi OTP
     */
    private OtpDeliveryMethod deliveryMethod;
    
    /**
     * Thời gian còn lại (tính bằng giây)
     */
    private long remainingTimeInSeconds;
}
