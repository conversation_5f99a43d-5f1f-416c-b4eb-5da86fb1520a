package org.pronexus.user.app.dtos.otp;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pronexus.user.domain.entity.OtpType;

/**
 * DTO cho request xác thực OTP
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerifyOtpRequestDto {

    /**
     * Mã OTP cần xác thực
     */
    @NotBlank(message = "Mã OTP không được để trống")
    @Pattern(regexp = "^\\d{6}$", message = "Mã OTP phải có 6 chữ số")
    private String code;

    /**
     * Loại OTP
     */
    @NotNull(message = "Loại OTP không được để trống")
    private OtpType type;

    /**
     * Thông tin liên hệ (số điện thoại) để xác thực OTP
     * Bắt buộc khi không có token xác thực
     */
    private String contactInfo;

    /**
     * Dữ liệu bổ sung liên quan đến việc xác thực (nếu cần)
     */
    private String additionalData;
}
