package org.pronexus.user.app.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDeletionWarningDto {
    private boolean canDelete;
    private List<String> warnings;
    private List<RelatedDataInfo> relatedData;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RelatedDataInfo {
        private String tableName;
        private String description;
        private long count;
    }
}
