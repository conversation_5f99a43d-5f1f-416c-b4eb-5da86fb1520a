package org.pronexus.user.app.dtos.otp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpStatus;
import org.pronexus.user.domain.entity.OtpType;

import java.time.LocalDateTime;

/**
 * DTO chung cho OTP
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpDto {
    
    /**
     * ID của OTP
     */
    private Long id;
    
    /**
     * Mã OTP
     */
    private String code;
    
    /**
     * Thời gian hết hạn
     */
    private LocalDateTime expiryTime;
    
    /**
     * Số lần đã thử xác thực
     */
    private Integer verificationAttempts;
    
    /**
     * Trạng thái OTP
     */
    private OtpStatus status;
    
    /**
     * Loại OTP
     */
    private OtpType type;
    
    /**
     * ID của người dùng
     */
    private String userId;
    
    /**
     * Thông tin liên hệ
     */
    private String contactInfo;
    
    /**
     * Phương thức gửi OTP
     */
    private OtpDeliveryMethod deliveryMethod;
    
    /**
     * Dữ liệu bổ sung
     */
    private String additionalData;
    
    /**
     * Thời gian tạo
     */
    private Long createdAt;
    
    /**
     * Người tạo
     */
    private String createdBy;
    
    /**
     * Thời gian cập nhật
     */
    private Long updatedAt;
    
    /**
     * Người cập nhật
     */
    private String updatedBy;
}
