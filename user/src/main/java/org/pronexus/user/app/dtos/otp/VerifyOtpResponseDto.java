package org.pronexus.user.app.dtos.otp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pronexus.user.domain.entity.OtpType;

/**
 * DTO cho response khi xác thực OTP
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerifyOtpResponseDto {
    
    /**
     * Kết quả xác thực
     */
    private boolean success;
    
    /**
     * Loại OTP
     */
    private OtpType type;
    
    /**
     * Thông báo
     */
    private String message;
    
    /**
     * Token xác thực (nếu cần)
     */
    private String verificationToken;
    
    /**
     * Số lần thử còn lại
     */
    private Integer remainingAttempts;
}
