package org.pronexus.user.app.dtos.otp;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpType;

/**
 * DTO cho request tạo OTP
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateOtpRequestDto {

    /**
     * Loại OTP cần tạo
     */
    @NotNull(message = "Loại OTP không được để trống")
    private OtpType type;

    /**
     * Thông tin liên hệ để gửi OTP (email hoặc số điện thoại)
     */
    @NotBlank(message = "Thông tin liên hệ không được để trống")
    private String contactInfo;

    /**
     * Phương thức gửi OTP
     * Mặc định là ZNS (Zalo Notification Service)
     */
    @Builder.Default
    private OtpDeliveryMethod deliveryMethod = OtpDeliveryMethod.ZNS;

    /**
     * Dữ liệu bổ sung liên quan đến OTP (nếu cần)
     */
    private String additionalData;

    /**
     * ID của template ZNS (nếu sử dụng ZNS)
     */
    private String templateId;
}
