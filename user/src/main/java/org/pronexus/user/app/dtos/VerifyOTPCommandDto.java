package org.pronexus.user.app.dtos;

import lombok.Data;
import jakarta.validation.constraints.NotEmpty;

@Data
public class VerifyOTPCommandDto {
    @NotEmpty(message = "Phone number is required")
    private String phoneNumber;

    @NotEmpty(message = "OTP is required")
    private String otp;

    @NotEmpty(message = "New password is required")
    private String newPassword;

    @NotEmpty(message = "Confirm password is required")
    private String confirmPassword;
}
