package org.pronexus.user.app.controllers;

import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.pronexus.user.app.dtos.*;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.pronexus.user.domain.service.core.UserService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final KeycloakClient keycloakClient;

    @GetMapping
    public ResponseEntity<Response<Page<UserWithRoleMappingModel>>> getUsers(
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "role", defaultValue = "true") boolean withRole,
            @RequestParam(value = "statistics", defaultValue = "true") boolean withStatistics) {
        Page<UserWithRoleMappingModel> body = userService.getUsers(PageRequest.of(page, size), username, withRole,
                withStatistics);
        return Response.success(body);
    }

    @GetMapping("/search")
    public ResponseEntity<Response<Page<UserRepresentation>>> searchUsers(
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size,
            @RequestParam(value = "keyword", required = false) String keyword) {
        Page<UserRepresentation> body;
        if (keyword == null || keyword.trim().isEmpty()) {
            body = userService.getUsersOnly(PageRequest.of(page, size), null);
        } else {
            body = keycloakClient.getUsers(PageRequest.of(page, size), keyword, keyword);
        }
        return Response.success(body);
    }

    @Validated
    @PostMapping("/register")
    public ResponseEntity<Response<CommandResponse<Void>>> register(
            @RequestBody @Valid final RegisterUserCommandDto command) {
        CommandResponse<Void> body = userService.registerUser(command);
        return Response.success(body);
    }

    @Validated
    @PostMapping("/register/partner")
    public ResponseEntity<Response<CommandResponse<UserRepresentation>>> registerPartnerUser(
            @RequestBody @Valid final RegisterUserCommandDto command) {
        CommandResponse<UserRepresentation> body = userService.registerPartnerUser(command);
        return Response.success(body);
    }

    @PostMapping("/login")
    public ResponseEntity<Response<CommandResponse<AccessTokenResponse>>> login(
            @RequestBody final LoginCommandDto command) {
        CommandResponse<AccessTokenResponse> body = userService.login(command);
        return Response.success(body);
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<Response<CommandResponse<AccessTokenResponse>>> refreshToken(
            @RequestBody final RefreshTokenCommand command) {
        CommandResponse<AccessTokenResponse> body = userService.refreshToken(command);
        return Response.success(body);
    }

    @GetMapping("/profile")
    public ResponseEntity<Response<UserProfileModelDto>> getUserInfo(
            @RequestParam(value = "role", defaultValue = "false") boolean withRole) {
        UserProfileModelDto body = userService.getUserProfile(withRole);
        return Response.success(body);
    }

    @PutMapping("/profile")
    public ResponseEntity<Response<CommandResponse<Void>>> updateUserInfo(
            @RequestBody final UpdateUserProfileCommandDto command) {
        CommandResponse<Void> body = userService.updateUserProfile(command);
        HttpStatus status = body.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
        return Response.of(status, body);
    }

    @PutMapping("/change-password")
    public ResponseEntity<Response<CommandResponse<Void>>> changePassword(
            @RequestBody final ChangePasswordCommandDto command) {
        CommandResponse<Void> body = userService.changePassword(command);
        HttpStatus status = body.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
        return Response.of(status, body);
    }

    @PutMapping("/{id}/assign-role")
    public ResponseEntity<Response<CommandResponse<Void>>> assignRole(@PathVariable String id,
            @RequestBody final List<RoleRepresentation> role) {
        CommandResponse<Void> body = userService.assignRole(id, role);
        HttpStatus status = body.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
        return Response.of(status, body);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public void handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        throw new DataValidationException(e.getMessage(), e.getBindingResult());
    }

    @PostMapping("/logout")
    public ResponseEntity<Response<CommandResponse<Void>>> logout() {
        CommandResponse<Void> body = userService.logout();
        HttpStatus status = body.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
        return Response.of(status, body);
    }

    @GetMapping("/check-exist")
    public ResponseEntity<Response<Boolean>> checkUsernameExist(@RequestParam("username") String username) {
        boolean exists = userService.usernameExisted(username);
        return Response.success(exists);
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<Response<CommandResponse<Void>>> forgotPassword(
            @Valid @RequestBody ForgotPasswordCommandDto command) {
        CommandResponse<Void> body = userService.forgotPassword(command);
        return Response.success(body);
    }

    @PostMapping("/verify-otp-reset-password")
    public ResponseEntity<Response<CommandResponse<Void>>> verifyOTPAndResetPassword(
            @Valid @RequestBody VerifyOTPCommandDto command) {
        CommandResponse<Void> body = userService.verifyOTPAndResetPassword(command);
        return Response.success(body);
    }

    @DeleteMapping("/{userId}")
    public ResponseEntity<Response<CommandResponse<Void>>> deleteUser(
            @PathVariable String userId,
            @RequestParam(value = "force", defaultValue = "false") boolean force) {
        CommandResponse<Void> body = userService.deleteUser(userId, force);
        return body.isSuccess() ? Response.success(body) : Response.failure(body);
    }

    @DeleteMapping("/{userId}/partner")
    public ResponseEntity<Response<CommandResponse<Void>>> deleteUserPartner(@PathVariable String userId) {
        CommandResponse<Void> body = userService.deleteUserPartner(userId);
        return body.isSuccess() ? Response.success(body) : Response.failure(body);
    }

    @DeleteMapping("/{userId}/devices")
    public ResponseEntity<Response<CommandResponse<Void>>> deleteUserDevices(@PathVariable String userId) {
        CommandResponse<Void> body = userService.deleteUserDevices(userId);
        return body.isSuccess() ? Response.success(body) : Response.failure(body);
    }

    @GetMapping("/{userId}/devices")
    public ResponseEntity<Response<List<UserDeviceRes>>> getUserDevices(@PathVariable String userId) {
        List<UserDeviceRes> devices = userService.getUserDevices(userId);
        return Response.success(devices);
    }
}
