package org.pronexus.user.domain.feign;

import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.*;
import org.keycloak.representations.idm.authorization.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(value = "identity", url = "${keycloak.auth-server-url}")
public interface KeycloakFeignClient {

        // =========== Common api ===========//

        @PostMapping(value = "/realms/master/protocol/openid-connect/token", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        AccessTokenResponse getMasterToken(@RequestBody Map<String, ?> request);

        @PostMapping(value = "/realms/" + "${keycloak.realm}"
                        + "/protocol/openid-connect/token", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        AccessTokenResponse getToken(@RequestBody Map<String, ?> request);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
        List<ClientRepresentation> getClients(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @RequestParam(value = "clientId") String clientId);

        // =========== Resource api ===========//

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/resource")
        List<ResourceRepresentation> getResources(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/resource/{resource_id}")
        ResourceRepresentation getResource(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "resource_id") String resourceId,
                        @PathVariable(value = "client_uuid") String clientUuid);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/resource/{resource_id}/scopes")
        ResourceRepresentation getScopesByResourceId(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "resource_id") String resourceId,
                        @PathVariable(value = "client_uuid") String clientUuid);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/resource")
        ResourceRepresentation createResource(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String masterToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @RequestBody ResourceRepresentation resource);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/resource/{resource_id}")
        ResourceRepresentation updateResource(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String masterToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "resource_id") String resourceId,
                        @RequestBody ResourceRepresentation resource);

        @DeleteMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/resource/{resource_id}")
        ResponseEntity<?> deleteResource(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String masterToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "resource_id") String resourceId);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/policy/evaluate")
        ResponseEntity<PolicyEvaluationResponse> evaluate(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @RequestBody PolicyEvaluationRequest evaluationRequest);

        // =========== Role api ===========//

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/roles")
        List<RoleRepresentation> getRoles(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/roles/{role_name}")
        RoleRepresentation getRole(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "role_name") String roleName);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/roles")
        RoleRepresentation createRole(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @RequestBody RoleRepresentation role);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/roles/{role_name}")
        RoleRepresentation updateRole(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "role_name") String roleName,
                        @RequestBody RoleRepresentation role);

        @DeleteMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/roles/{role_name}")
        void deleteRole(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "role_name") String roleName);

        // =========== Scope api ===========//

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/scope")
        List<ScopeRepresentation> getScopes(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/scope/{scope_id}")
        ScopeRepresentation getScopeById(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "scope_id") String scopeId);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/scope")
        ScopeRepresentation createScope(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @RequestBody ScopeRepresentation scope);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/scope/{scope_id}")
        ScopeRepresentation updateScope(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "scope_id") String scopeId,
                        @RequestBody ScopeRepresentation scope);

        @DeleteMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/scope/{scope_id}")
        ResponseEntity<?> deleteScope(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "scope_id") String scopeId);

        // =========== Permission api ===========//

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission")
        List<AbstractPolicyRepresentation> getPermissions(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission/{permission_id}")
        AbstractPolicyRepresentation getPermission(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "permission_id") String permissionId,
                        @PathVariable(value = "client_uuid") String clientUuid);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission/scope")
        AbstractPolicyRepresentation createScopePermission(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @RequestBody AbstractPolicyRepresentation scopePermission);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission/resource")
        AbstractPolicyRepresentation createResourcePermission(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @RequestBody AbstractPolicyRepresentation resourcePermission);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission/scope/{permission_id}")
        AbstractPolicyRepresentation updateScopePermission(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "permission_id") String permissionId,
                        @RequestBody AbstractPolicyRepresentation scope);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission/resource/{permission_id}")
        AbstractPolicyRepresentation updateResourcePermission(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "permission_id") String permissionId,
                        @RequestBody AbstractPolicyRepresentation scope);

        @DeleteMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/permission/{permission_id}")
        void deletePermission(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "permission_id") String permissionId);

        @PostMapping("/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/policy/role")
        RolePolicyRepresentation createRolePolicy(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @RequestBody RolePolicyRepresentation data);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/policy/{policy_id}/associatedPolicies")
        List<AbstractPolicyRepresentation> getAssociatedPoliciesOfPolicy(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "policy_id") String policyId);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/policy/{policy_id}/scopes")
        List<ScopeRepresentation> getScopesOfPolicy(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "policy_id") String policyId);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/clients/{client_uuid}/authz/resource-server/policy/{policy_id}/resources")
        List<ResourceRepresentation> getResourcesOfPolicy(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "client_uuid") String clientUuid,
                        @PathVariable(value = "policy_id") String policyId);

        // =========== User api ===========//

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users")
        List<UserRepresentation> getUsers(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @RequestParam(value = "username", required = false) String username,
                        @RequestParam(value = "email", required = false) String email,
                        @RequestParam(value = "first") Integer first,
                        @RequestParam(value = "max") Integer max);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/count")
        Integer countUser(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestParam(value = "username") String username,
                        @RequestParam(value = "email") String email);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users")
        List<UserRepresentation> findUserByUsername(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestParam(value = "username") String username,
                        @RequestParam(value = "exact") Boolean exact);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/{user_id}")
        UserRepresentation findUserById(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users")
        void createUser(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestBody UserRepresentation data);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/{user_id}")
        void updateUser(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId,
                        @RequestBody UserRepresentation data);

        @PutMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/{user_id}/reset-password")
        void resetPassword(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId,
                        @RequestBody CredentialRepresentation data);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/{user_id}/role-mappings/realm")
        void assignRole(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId,
                        @RequestBody List<RoleRepresentation> data);

        @GetMapping(value = "/realms/" + "${keycloak.realm}" + "/authz/protection/resource_set")
        List<ResourceRepresentation> getResourcesSet(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @RequestParam(value = "deep", defaultValue = "true") boolean deep);

        @GetMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/{user_id}/role-mappings")
        MappingsRepresentation getRoleMappings(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId);

        @GetMapping(value = "/admin/realms/${keycloak.realm}/users/{user-id}/sessions", produces = MediaType.APPLICATION_JSON_VALUE)
        List<UserSessionRepresentation> getUserSessions(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String clientToken,
                        @PathVariable(value = "user-id") String userId);

        @PostMapping(value = "/admin/realms/" + "${keycloak.realm}"
                        + "/users/{user_id}/logout", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        ResponseEntity<Void> logout(@RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId);

        @DeleteMapping(value = "/admin/realms/" + "${keycloak.realm}" + "/users/{user_id}")
        void deleteUser(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "user_id") String userId);
}
