package org.pronexus.user.domain.model.external;

import lombok.Data;

/**
 * Model cho thông tin thiết bị của user từ Portal service
 */
@Data
public class UserDeviceModel {
    private Integer id;
    private String userId;
    private String deviceToken;
    private String deviceType;
    private Object deviceInfo; // JSON object
    private String status;
    private Long createdAt;
    private String createdBy;
    private boolean isSoftDeleted;
    private Long updatedAt;
    private String updatedBy;
}
