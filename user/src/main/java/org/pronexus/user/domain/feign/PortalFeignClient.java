package org.pronexus.user.domain.feign;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import org.pronexus.user.domain.model.external.EmployeeModel;
import org.pronexus.user.domain.model.external.PartnerModel;
import org.pronexus.user.domain.model.external.UserDeviceModel;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "portal", url = "${service.portal.url}")
public interface PortalFeignClient {

        @GetMapping("/api/v1/employee/query")
        Response<EmployeeModel> queryEmployee(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestParam(value = "q") String query);

        @PutMapping("/api/v1/employee/{id}")
        Response<CommandResponse<EmployeeModel>> updateEmployee(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(value = "id") Long id,
                        @RequestBody EmployeeModel command);

        @GetMapping("/api/v1/partner/{id}")
        Response<PartnerModel> getPartner(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(name = "id") Long id);

        @GetMapping("/api/v1/partner/kc-user/{id}")
        Response<PartnerModel> getPartnerByKeycloakId(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(name = "id") String id);

        @DeleteMapping("/api/v1/user/partner/{userId}")
        Response<Boolean> deleteUserPartner(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(name = "userId") String userId);

        @DeleteMapping("/api/v1/user/device/{userId}")
        Response<Boolean> deleteUserDevices(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @PathVariable(name = "userId") String userId);

        @GetMapping("/api/v1/user/device")
        Response<List<UserDeviceRes>> getUserDevices(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestParam(name = "userId") String userId);
}
