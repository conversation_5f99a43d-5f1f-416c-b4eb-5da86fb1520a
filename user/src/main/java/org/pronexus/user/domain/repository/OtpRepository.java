package org.pronexus.user.domain.repository;

import org.pronexus.user.domain.entity.Otp;
import org.pronexus.user.domain.entity.OtpStatus;
import org.pronexus.user.domain.entity.OtpType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository để thao tác với entity Otp
 */
@Repository
public interface OtpRepository extends JpaRepository<Otp, Long> {

    /**
     * Tìm OTP theo mã và userId
     *
     * @param code Mã OTP
     * @param userId ID của người dùng
     * @return Optional chứa OTP nếu tìm thấy
     */
    Optional<Otp> findByCodeAndUserId(String code, String userId);

    /**
     * Tìm <PERSON>TP hợp lệ (chư<PERSON> hết hạn) theo mã, userId và trạng thái
     *
     * @param code Mã OTP
     * @param userId ID của người dùng
     * @param status Trạng thái OTP
     * @param currentTime Thời gian hiện tại
     * @return Optional chứa OTP nếu tìm thấy
     */
    @Query("SELECT o FROM Otp o WHERE o.code = :code AND o.userId = :userId AND o.status = :status AND o.expiryTime > :currentTime")
    Optional<Otp> findValidOtp(
            @Param("code") String code,
            @Param("userId") String userId,
            @Param("status") OtpStatus status,
            @Param("currentTime") LocalDateTime currentTime);

    /**
     * Tìm OTP theo mã, userId và trạng thái
     *
     * @param code Mã OTP
     * @param userId ID của người dùng
     * @param status Trạng thái OTP
     * @return Optional chứa OTP nếu tìm thấy
     */
    Optional<Otp> findByCodeAndUserIdAndStatus(String code, String userId, OtpStatus status);

    /**
     * Tìm OTP theo userId, loại và trạng thái
     *
     * @param userId ID của người dùng
     * @param type Loại OTP
     * @param status Trạng thái OTP
     * @return Danh sách OTP thỏa mãn điều kiện
     */
    List<Otp> findByUserIdAndTypeAndStatus(String userId, OtpType type, OtpStatus status);

    /**
     * Tìm OTP mới nhất theo userId và loại
     *
     * @param userId ID của người dùng
     * @param type Loại OTP
     * @return Optional chứa OTP nếu tìm thấy
     */
    @Query("SELECT o FROM Otp o WHERE o.userId = :userId AND o.type = :type ORDER BY o.createdAt DESC")
    Optional<Otp> findLatestByUserIdAndType(@Param("userId") String userId, @Param("type") OtpType type);

    /**
     * Đếm số lượng OTP đã tạo cho một người dùng trong khoảng thời gian
     *
     * @param userId ID của người dùng
     * @param type Loại OTP
     * @param startTime Thời gian bắt đầu
     * @return Số lượng OTP đã tạo
     */
    @Query("SELECT COUNT(o) FROM Otp o WHERE o.userId = :userId AND o.type = :type AND o.createdAt >= :startTime")
    int countByUserIdAndTypeAndCreatedAtAfter(
            @Param("userId") String userId,
            @Param("type") OtpType type,
            @Param("startTime") Long startTime);

    /**
     * Tìm tất cả OTP đã hết hạn nhưng chưa được đánh dấu là EXPIRED
     *
     * @param currentTime Thời gian hiện tại
     * @param status Trạng thái PENDING
     * @return Danh sách OTP đã hết hạn
     */
    @Query("SELECT o FROM Otp o WHERE o.expiryTime < :currentTime AND o.status = :status")
    List<Otp> findAllExpiredButNotMarked(
            @Param("currentTime") LocalDateTime currentTime,
            @Param("status") OtpStatus status);

    /**
     * Đếm số lượng OTP theo userId
     *
     * @param userId ID của người dùng
     * @return Số lượng OTP
     */
    long countByUserId(String userId);

    /**
     * Xóa tất cả OTP theo userId
     *
     * @param userId ID của người dùng
     */
    void deleteByUserId(String userId);
}
