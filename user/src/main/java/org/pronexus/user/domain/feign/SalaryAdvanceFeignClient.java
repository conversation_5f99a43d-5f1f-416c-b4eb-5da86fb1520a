package org.pronexus.user.domain.feign;

import com.salaryadvance.commonlibrary.rest.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "salary-advance-service", url = "${feign.salary-advance.url}")
public interface SalaryAdvanceFeignClient {

    /**
     * <PERSON><PERSON>a tất cả dữ liệu salary advance của employee
     * <PERSON><PERSON> gồm: transaction, transaction_aud, salary_advance_request, salary_advance_request_aud
     */
    @DeleteMapping("/api/v1/employee/{employeeId}/all-data")
    Response<Boolean> deleteAllEmployeeData(
            @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
            @PathVariable(name = "employeeId") String employeeId);
}
