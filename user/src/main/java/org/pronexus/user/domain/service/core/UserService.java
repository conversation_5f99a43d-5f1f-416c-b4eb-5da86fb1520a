package org.pronexus.user.domain.service.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.pronexus.user.app.dtos.*;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface UserService {
    Page<UserWithRoleMappingModel> getUsers(Pageable pageable, String username, boolean withRoles);

    Page<UserRepresentation> getUsersOnly(Pageable pageable, String username);

    CommandResponse<Void> registerUser(RegisterUserCommandDto command);

    CommandResponse<UserRepresentation> registerPartnerUser(RegisterUserCommandDto command);

    CommandResponse<AccessTokenResponse> login(LoginCommandDto command);

    CommandResponse<AccessTokenResponse> refreshToken(RefreshTokenCommand command);

    UserProfileModelDto getUserProfile(boolean withRole);

    CommandResponse<Void> updateUserProfile(UpdateUserProfileCommandDto command);

    CommandResponse<Void> changePassword(ChangePasswordCommandDto command);

    CommandResponse<Void> assignRole(String userId, List<RoleRepresentation> role);

    CommandResponse<Void> logout();

    boolean usernameExisted(String username);

    CommandResponse<Void> forgotPassword(ForgotPasswordCommandDto command);

    CommandResponse<Void> verifyOTPAndResetPassword(VerifyOTPCommandDto command);

    UserDeletionWarningDto checkUserDeletionWarnings(String userId);

    CommandResponse<Void> deleteUser(String userId, boolean force);

    /**
     * Xóa liên kết user-partner theo userId
     * 
     * @param userId ID của user
     * @return CommandResponse xác nhận kết quả
     */
    CommandResponse<Void> deleteUserPartner(String userId);

    /**
     * Xóa tất cả thiết bị của user theo userId
     * 
     * @param userId ID của user
     * @return CommandResponse xác nhận kết quả
     */
    CommandResponse<Void> deleteUserDevices(String userId);

    /**
     * Lấy danh sách thiết bị của user theo userId
     *
     * @param userId ID của user
     * @return Danh sách thiết bị
     */
    List<UserDeviceRes> getUserDevices(String userId);
}
