package org.pronexus.user.domain.entity;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * Entity lưu trữ thông tin OTP
 * Sử dụng để xác thực người dùng khi tìm lại mật khẩu hoặc xác thực các nghiệp vụ
 */
@Entity
@Table(name = "otp", schema = "user_schema")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Otp extends AuditableEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Mã OTP được tạo
     */
    @Column(nullable = false, length = 6)
    private String code;

    /**
     * Thời gian hết hạn của OTP
     */
    @Column(nullable = false)
    private LocalDateTime expiryTime;

    /**
     * Số lần đã thử xác thực
     */
    @Column(nullable = true)
    private Integer verificationAttempts;

    /**
     * Trạng thái của OTP: PENDING, VERIFIED, EXPIRED
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OtpStatus status;

    /**
     * Loại OTP: PASSWORD_RESET, TRANSACTION_VERIFICATION, REGISTRATION
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OtpType type;

    /**
     * ID của người dùng liên quan đến OTP
     */
    @Column(nullable = false)
    private String userId;

    /**
     * Thông tin liên hệ để gửi OTP (email hoặc số điện thoại)
     */
    @Column(nullable = false)
    private String contactInfo;

    /**
     * Phương thức gửi OTP: EMAIL, SMS
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OtpDeliveryMethod deliveryMethod;

    /**
     * Dữ liệu bổ sung liên quan đến OTP (nếu cần)
     */
    @Column(columnDefinition = "TEXT")
    private String additionalData;

    /**
     * Kiểm tra OTP có hết hạn không
     */
    @Transient
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiryTime);
    }

    /**
     * Kiểm tra OTP có vượt quá số lần thử không
     */
    @Transient
    public boolean isMaxAttemptsReached(int maxAttempts) {
        return verificationAttempts >= maxAttempts;
    }
}
