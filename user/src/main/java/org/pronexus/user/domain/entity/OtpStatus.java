package org.pronexus.user.domain.entity;

/**
 * Enum định nghĩa các trạng thái của OTP
 */
public enum OtpStatus {
    /**
     * OTP đã được tạo và đang chờ xác thực
     */
    PENDING,
    
    /**
     * OTP đã được xác thực thành công
     */
    VERIFIED,
    
    /**
     * OTP đã hết hạn
     */
    EXPIRED,
    
    /**
     * OTP đã bị vô hiệu hóa (ví dụ: do quá nhiều lần thử không thành công)
     */
    INVALIDATED
}
