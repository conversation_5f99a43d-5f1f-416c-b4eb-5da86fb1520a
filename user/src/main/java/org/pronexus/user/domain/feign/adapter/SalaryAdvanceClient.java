package org.pronexus.user.domain.feign.adapter;

import com.salaryadvance.commonlibrary.rest.Response;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.feign.SalaryAdvanceFeignClient;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SalaryAdvanceClient {

    private final SalaryAdvanceFeignClient salaryAdvanceFeignClient;
    private final KeycloakClient keycloakClient;

    /**
     * X<PERSON>a tất cả dữ liệu salary advance của employee
     * 
     * @param employeeId ID của employee
     * @return true nếu thành công
     */
    public boolean deleteAllEmployeeData(String employeeId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = salaryAdvanceFeignClient.deleteAllEmployeeData(token, employeeId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE SALARY_ADVANCE_DATA] Cannot delete salary advance data for employee {}: {}", 
                    employeeId, e.getMessage(), e);
            return false;
        }
    }
}
