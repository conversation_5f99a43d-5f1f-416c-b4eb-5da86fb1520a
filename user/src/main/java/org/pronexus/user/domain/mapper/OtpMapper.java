package org.pronexus.user.domain.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.app.dtos.otp.OtpDto;
import org.pronexus.user.domain.entity.Otp;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * Mapper để chuyển đổi giữa Otp entity và các DTO
 */
@Mapper(componentModel = "spring")
public interface OtpMapper {
    
    /**
     * Chuyển đổi từ Otp entity sang OtpDto
     * 
     * @param otp Entity cần chuyển đổi
     * @return OtpDto tương ứng
     */
    OtpDto toOtpDto(Otp otp);
    
    /**
     * Chuyển đổi từ Otp entity sang GenerateOtpResponseDto
     * 
     * @param otp Entity cần chuyển đổi
     * @return GenerateOtpResponseDto tương ứng
     */
    @Mapping(target = "maskedContactInfo", source = "contactInfo", qualifiedByName = "maskContactInfo")
    @Mapping(target = "remainingTimeInSeconds", source = "expiryTime", qualifiedByName = "calculateRemainingTime")
    GenerateOtpResponseDto toGenerateOtpResponseDto(Otp otp);
    
    /**
     * Che một phần thông tin liên hệ
     * 
     * @param contactInfo Thông tin liên hệ gốc
     * @return Thông tin liên hệ đã được che
     */
    @Named("maskContactInfo")
    default String maskContactInfo(String contactInfo) {
        if (contactInfo == null || contactInfo.isEmpty()) {
            return "";
        }
        
        // Nếu là email
        if (contactInfo.contains("@")) {
            String[] parts = contactInfo.split("@");
            String name = parts[0];
            String domain = parts[1];
            
            if (name.length() <= 2) {
                return name + "@" + domain;
            }
            
            return name.substring(0, 2) + "***" + "@" + domain;
        } 
        // Nếu là số điện thoại
        else {
            if (contactInfo.length() <= 4) {
                return contactInfo;
            }
            
            return "***" + contactInfo.substring(contactInfo.length() - 4);
        }
    }
    
    /**
     * Tính thời gian còn lại (tính bằng giây)
     * 
     * @param expiryTime Thời gian hết hạn
     * @return Số giây còn lại
     */
    @Named("calculateRemainingTime")
    default long calculateRemainingTime(LocalDateTime expiryTime) {
        if (expiryTime == null) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expiryTime)) {
            return 0;
        }
        
        return Duration.between(now, expiryTime).getSeconds();
    }
}
