package org.pronexus.user.domain.entity;

/**
 * Enum định nghĩa các loại OTP
 */
public enum OtpType {
    /**
     * OTP dùng để đặt lại mật khẩu
     */
    PASSWORD_RESET,
    
    /**
     * OTP dùng để xác thực giao dịch
     */
    TRANSACTION_VERIFICATION,
    
    /**
     * OTP dùng để xác thực đăng ký tài khoản
     */
    REGISTRATION,
    
    /**
     * OTP dùng để xác thực đăng nhập
     */
    LOGIN_VERIFICATION
}
