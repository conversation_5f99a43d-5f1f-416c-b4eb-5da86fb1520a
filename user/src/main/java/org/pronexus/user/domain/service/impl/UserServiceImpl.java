package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.constants.ApiMessage;
import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.exception.base.ResourceExistedException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.utils.StringUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.MappingsRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.keycloak.representations.idm.UserSessionRepresentation;
import org.pronexus.user.app.dtos.*;
import org.pronexus.user.app.dtos.otp.GenerateOtpRequestDto;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpRequestDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpResponseDto;
import com.salaryadvance.commonlibrary.enums.RoleType;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpType;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;
import org.pronexus.user.domain.feign.adapter.PortalClient;
import org.pronexus.user.domain.feign.adapter.SalaryAdvanceClient;
import org.pronexus.user.domain.mapper.UserMapper;
import org.pronexus.user.domain.model.external.EmployeeModel;
import org.pronexus.user.domain.model.external.PartnerModel;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.pronexus.user.domain.service.core.RoleService;
import org.pronexus.user.domain.service.core.UserService;
import org.pronexus.user.domain.service.core.OtpService;
import org.pronexus.user.domain.repository.OtpRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final KeycloakClient keycloakClient;
    private final PortalClient portalClient;
    private final SalaryAdvanceClient salaryAdvanceClient;
    private final UserMapper userMapper;
    private final RoleService roleService;
    private final OtpService otpService;
    private final OtpRepository otpRepository;

    @Override
    public Page<UserWithRoleMappingModel> getUsers(Pageable pageable, String username, boolean withRoles) {
        return getUsers(pageable, username, withRoles, false);
    }

    @Override
    public Page<UserWithRoleMappingModel> getUsers(Pageable pageable, String username, boolean withRoles,
            boolean withStatistics) {
        return keycloakClient.getUsers(pageable, username).map(u -> {
            UserWithRoleMappingModel userWithRole = new UserWithRoleMappingModel();
            userWithRole.setUser(u);

            if (withRoles) {
                MappingsRepresentation roleMapping = keycloakClient.findRoles(u.getId());
                userWithRole.setRoles(roleMapping);
            }

            // REMOVED: Statistics calculation để cải thiện performance
            // Statistics sẽ được tính trong getUserDetail API

            return userWithRole;
        });
    }

    @Override
    public Page<UserRepresentation> getUsersOnly(Pageable pageable, String username) {
        return keycloakClient.getUsers(pageable, username);
    }

    @Override
    @Transactional
    public CommandResponse<Void> registerUser(RegisterUserCommandDto command) {
        register(command);

        // Thông báo cho người dùng cần xác thực OTP
        log.info("User is created and needs OTP verification: {}", command.getUsername());
        return CommandResponse.success(null, "Tài khoản đã được tạo. Vui lòng xác thực OTP để kích hoạt tài khoản.");
    }

    private void register(RegisterUserCommandDto command) {
        boolean exists = keycloakClient.usernameExisted(command.getUsername());
        if (exists)
            throw new ResourceExistedException(String.format("Username %s already exists!", command.getUsername()),
                    null);
        UserRepresentation registrationData = userMapper.collectRegistrationData(command);
        keycloakClient.createUser(registrationData);
        log.info("User is created: {}", command.getUsername());
    }

    @Override
    @Transactional
    public CommandResponse<UserRepresentation> registerPartnerUser(RegisterUserCommandDto command) {
        register(command);
        UserRepresentation registeredUser = keycloakClient.findUserByUsername(command.getUsername());
        if (command.getRoles() != null && !command.getRoles().isEmpty()) {
            assignRole(command.getRoles(), registeredUser.getId());
        }

        // Thông báo cho người dùng cần xác thực OTP
        log.info("Partner user is created and needs OTP verification: {}", command.getUsername());
        return CommandResponse.success(registeredUser,
                "Tài khoản đối tác đã được tạo. Vui lòng xác thực OTP để kích hoạt tài khoản.");
    }

    @Override
    public CommandResponse<AccessTokenResponse> login(LoginCommandDto command) {
        // check user exists
        boolean exists = keycloakClient.usernameExisted(command.getUsername());
        if (!exists)
            throw new BadRequestException(
                    "Số điện thoại này chưa đăng ký tài khoản trên hệ thống, vui lòng kiểm tra lại.");

        // Kiểm tra tài khoản đã được xác thực chưa
        UserRepresentation user = keycloakClient.findUserByUsername(command.getUsername());
        if (!user.isEnabled()) {
            Map<String, List<String>> attributes = user.getAttributes();
            if (attributes != null && attributes.containsKey("registration_complete")) {
                List<String> registrationComplete = attributes.get("registration_complete");
                if (registrationComplete != null && !registrationComplete.isEmpty()
                        && "false".equals(registrationComplete.get(0))) {
                    throw new BadRequestException(
                            "Tài khoản chưa được xác thực. Vui lòng xác thực OTP để kích hoạt tài khoản.");
                }
            }
        }

        try {
            AccessTokenResponse accessToken = keycloakClient.getToken(command.getUsername(), command.getPassword());
            log.info("User {} is logged in!", command.getUsername());
            return CommandResponse.success(accessToken, ApiMessage.loggedIn());
        } catch (Exception e) {
            log.error("Login failed for user {}: {}", command.getUsername(), e.getMessage());
            // Return specific error message for incorrect password
            throw new BadRequestException("Mật khẩu bạn nhập không đúng, vui lòng kiểm tra và nhập lại.", e);
        }
    }

    @Override
    public CommandResponse<AccessTokenResponse> refreshToken(RefreshTokenCommand command) {
        AccessTokenResponse accessToken = keycloakClient.refreshToken(command.getRefreshToken());
        log.info("Refresh token success fully");
        return CommandResponse.success(accessToken, "Refresh token successfully");
    }

    @Override
    public UserProfileModelDto getUserProfile(boolean withRole) {
        String userId = TokenUtils.getUserId();
        UserRepresentation user = keycloakClient.findUserById(userId);
        EmployeeModel profile = portalClient.getEmployeeByUserId(userId);
        List<UserSessionRepresentation> sessions = keycloakClient.getUserSessions(userId);

        UserProfileModelDto returnData = userMapper.toUserProfileModel(user, profile);
        returnData.setAvatar(getAttributeValue(user, "avatar"));
        returnData.setEmail(user.getEmail());

        // Xử lý partner dựa vào role
        if (roleService.hasRole(RoleType.PORTAL_ADMIN)) {
            PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
            if (partner != null) {
                returnData.setPartnerId(partner.getId());
                returnData.setPartner(partner);
            }
        } else if (profile != null) {
            Long partnerId = profile.getPartnerId();
            if (partnerId != null) {
                returnData.setPartner(portalClient.getPartnerById(partnerId));
            }
        }

        // Xử lý role mapping và sessions
        if (withRole) {
            returnData.setRoleMapping(keycloakClient.findRoles(userId));
        }
        returnData.setUserSessions(sessions);

        return returnData;
    }

    @Override
    @Transactional
    public CommandResponse<Void> updateUserProfile(UpdateUserProfileCommandDto command) {
        String userId = TokenUtils.getUserId();
        UserRepresentation user = keycloakClient.findUserById(userId);

        if (command.getPartnerId() != null && command.getCode() != null) {
            // Case 1: User trying to map with employee
            String query = "partnerId:eq:" + command.getPartnerId() + "&" + "code:eq:" + command.getCode();
            EmployeeModel employee = portalClient.queryEmployee(query);
            if (Objects.nonNull(employee)) {
                if (!StringUtils.isNullOrEmpty(employee.getUserId()) && !employee.getUserId().equals(userId)) {
                    return CommandResponse.failure(null, "Nhân sự đã được gán cho user khác");
                }
                user.setFirstName(employee.getName());
                user.setLastName(employee.getName());
                user.setEmail(employee.getEmail());

                // Update user attributes and save to Keycloak
                updateUserAndSave(user, command.getAvatar(), command.getEmail());

                // Update employee mapping
                employee.setUserId(user.getId());
                employee.setAvatar(command.getAvatar());
                portalClient.updateEmployee(employee.getId(), employee);
                log.info("User {} has profile updated with employee mapping and avatar!", userId);
                return CommandResponse.success(null, ApiMessage.updated("User profile"));
            } else {
                log.error("Employee with partner id {} and code {} does not exist!", command.getPartnerId(),
                        command.getCode());
                return CommandResponse.failure(null, ApiMessage.notFound("Employee"));
            }
        } else {
            // Case 2: User only updating Keycloak attributes
            updateUserAndSave(user, command.getAvatar(), command.getEmail());
            log.info("User {} has profile updated in Keycloak only!", userId);
            return CommandResponse.success(null, ApiMessage.updated("User profile"));
        }
    }

    @Override
    public CommandResponse<Void> changePassword(ChangePasswordCommandDto command) {
        try {
            validateCurrentPassword(command.getOldPassword());
            validateNewPassword(command);
            String userId = TokenUtils.getUserId();
            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(command.getNewPassword());
            credential.setTemporary(false);
            keycloakClient.resetPassword(userId, credential);
            return CommandResponse.success(null, ApiMessage.updated("Password"));
        } catch (Exception e) {
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> assignRole(String userId, List<RoleRepresentation> role) {
        try {
            keycloakClient.assignRole(userId, role);
            return CommandResponse.success(null, "Assigned role successfully");
        } catch (Exception e) {
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    private void validateCurrentPassword(String currentPassword) {
        try {
            keycloakClient.getToken(TokenUtils.getUsername(), currentPassword);
        } catch (Exception e) {
            log.error("Old password not correct!, {}", e.getMessage());
            throw new DataValidationException("Old password not correct", Map.of("details", e.getMessage()));
        }
    }

    private void validateNewPassword(ChangePasswordCommandDto command) {
        if (!command.getNewPassword().equals(command.getConfirmPassword())) {
            log.error("Confirmation password not match!");
            throw new DataValidationException("Confirmation password not match", null);
        }
    }

    private void assignRole(List<String> roles, String userId) {
        List<RoleRepresentation> rolesRepresentation = roles.stream().map(keycloakClient::getRole).toList();
        keycloakClient.assignRole(userId, rolesRepresentation);
    }

    @Override
    public CommandResponse<Void> logout() {
        try {
            keycloakClient.logout();
            return CommandResponse.success(null, "Logged out successfully");
        } catch (Exception e) {
            log.error("Failed to logout: {}", e.getMessage());
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    @Override
    public boolean usernameExisted(String username) {
        return keycloakClient.usernameExisted(username);
    }

    @Override
    public CommandResponse<Void> forgotPassword(ForgotPasswordCommandDto command) {
        try {
            // Kiểm tra số điện thoại tồn tại
            if (!keycloakClient.usernameExisted(command.getPhoneNumber())) {
                return CommandResponse.failure(null, "Số điện thoại chưa được đăng ký");
            }

            // Tạo yêu cầu gửi OTP
            GenerateOtpRequestDto otpRequest = GenerateOtpRequestDto.builder()
                    .type(OtpType.PASSWORD_RESET)
                    .contactInfo(command.getPhoneNumber())
                    .deliveryMethod(OtpDeliveryMethod.ZNS)
                    .build();

            // Gửi OTP thông qua OTP service
            CommandResponse<GenerateOtpResponseDto> otpResponse = otpService.generateOtp(otpRequest);

            if (!otpResponse.isSuccess()) {
                return CommandResponse.failure(null, "Không thể gửi OTP: " + otpResponse.getMessage());
            }

            return CommandResponse.success(null, "Mã OTP đã được gửi đến số điện thoại của bạn");
        } catch (Exception e) {
            log.error("Lỗi khi xử lý yêu cầu quên mật khẩu", e);
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> verifyOTPAndResetPassword(VerifyOTPCommandDto command) {
        try {
            // Kiểm tra số điện thoại tồn tại
            if (!keycloakClient.usernameExisted(command.getPhoneNumber())) {
                return CommandResponse.failure(null, "Số điện thoại không tồn tại");
            }

            // Xác thực OTP
            VerifyOtpRequestDto verifyRequest = VerifyOtpRequestDto.builder()
                    .type(OtpType.PASSWORD_RESET)
                    .contactInfo(command.getPhoneNumber())
                    .code(command.getOtp())
                    .build();

            CommandResponse<VerifyOtpResponseDto> verifyResponse = otpService.verifyOtp(verifyRequest, true);

            if (!verifyResponse.isSuccess()) {
                return CommandResponse.failure(null, "OTP không hợp lệ hoặc đã hết hạn");
            }

            // Kiểm tra mật khẩu mới
            if (!command.getNewPassword().equals(command.getConfirmPassword())) {
                return CommandResponse.failure(null, "Mật khẩu xác nhận không khớp");
            }

            if (!isValidPassword(command.getNewPassword())) {
                return CommandResponse.failure(null, "Mật khẩu không đáp ứng yêu cầu bảo mật");
            }

            // Lấy user từ Keycloak và đổi mật khẩu
            UserRepresentation user = keycloakClient.findUserByUsername(command.getPhoneNumber());
            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(command.getNewPassword());
            credential.setTemporary(false);
            keycloakClient.resetPassword(user.getId(), credential);

            return CommandResponse.success(null, "Đặt lại mật khẩu thành công");
        } catch (Exception e) {
            log.error("Lỗi khi xác thực OTP và đặt lại mật khẩu", e);
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    private boolean isValidPassword(String password) {
        // Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự
        // đặc biệt
        String pattern = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$";
        return password.matches(pattern);
    }

    /**
     * Updates user attributes and email in the UserRepresentation object
     *
     * @param user   UserRepresentation object to update
     * @param avatar Avatar URL to set (can be null or empty)
     * @param email  Email to set (can be null or empty)
     */
    private void updateUserAttributes(UserRepresentation user, String avatar, String email) {
        // Update avatar attribute if provided
        if (!StringUtils.isNullOrEmpty(avatar)) {
            Map<String, List<String>> attributes = user.getAttributes();
            if (attributes == null) {
                attributes = new HashMap<>();
                user.setAttributes(attributes);
            }
            attributes.put("avatar", List.of(avatar));
        }

        // Update email if provided
        if (!StringUtils.isNullOrEmpty(email)) {
            user.setEmail(email);
        }
    }

    /**
     * Updates user attributes and saves to Keycloak
     * 
     * @param user   UserRepresentation object to update
     * @param avatar Avatar URL to set (can be null or empty)
     * @param email  Email to set (can be null or empty)
     */
    private void updateUserAndSave(UserRepresentation user, String avatar, String email) {
        updateUserAttributes(user, avatar, email);
        keycloakClient.updateUser(user.getId(), user);
    }

    /**
     * Retrieves the first value of a specific attribute from user's attributes map
     * 
     * @param user          UserRepresentation object containing attributes
     * @param attributeName Name of the attribute to retrieve
     * @return The first value of the attribute or empty string if not found or
     *         exception occurs
     */
    private String getAttributeValue(UserRepresentation user, String attributeName) {
        try {
            if (user.getAttributes() == null) {
                return "";
            }

            Map<String, List<String>> attributes = user.getAttributes();
            List<String> valueList = attributes.get(attributeName);

            if (valueList != null && !valueList.isEmpty()) {
                return valueList.get(0);
            }

            return "";
        } catch (Exception e) {
            log.debug("Error getting attribute {} for user: {}", attributeName, e.getMessage());
            return "";
        }
    }

    @Override
    @Transactional
    public CommandResponse<Void> deleteUser(String userId, boolean force) {
        List<String> deletionLog = new ArrayList<>();
        List<Exception> errors = new ArrayList<>();

        try {
            // Kiểm tra user tồn tại
            UserRepresentation user = keycloakClient.findUserById(userId);
            if (user == null) {
                return CommandResponse.failure(null, "User không tồn tại");
            }

            // PHASE 1: Xóa dữ liệu local (SQL Transaction - ACID guaranteed)
            deleteUserDataInSameDatabase(userId, deletionLog);

            // PHASE 2: Xóa dữ liệu external services (với error handling)
            if (force) {
                deleteUserDataInExternalServices(userId, deletionLog, errors);
            }

            // PHASE 3: Xóa user khỏi Keycloak (cuối cùng vì không thể rollback)
            deleteUserFromKeycloak(userId, deletionLog, errors);

            // PHASE 4: Xử lý kết quả và logging
            return handleDeletionResult(userId, force, deletionLog, errors);

        } catch (Exception e) {
            log.error("Lỗi nghiêm trọng khi xóa user {}: {}", userId, e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi nghiêm trọng khi xóa user: " + e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> deleteUserPartner(String userId) {
        try {
            // Gọi Portal service để xóa user_partner
            boolean deleted = portalClient.deleteUserPartner(userId);
            if (deleted) {
                log.info("Successfully deleted user_partner for user: {}", userId);
                return CommandResponse.success(null, "Đã xóa liên kết user-partner thành công");
            } else {
                log.warn("No user_partner found for user: {}", userId);
                return CommandResponse.success(null, "Không tìm thấy liên kết user-partner");
            }
        } catch (Exception e) {
            log.error("Error deleting user_partner for user {}: {}", userId, e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi xóa liên kết user-partner: " + e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> deleteUserDevices(String userId) {
        try {
            // Gọi Portal service để xóa user devices
            boolean deleted = portalClient.deleteUserDevices(userId);
            if (deleted) {
                log.info("Successfully deleted user devices for user: {}", userId);
                return CommandResponse.success(null, "Đã xóa thiết bị của user thành công");
            } else {
                log.warn("No user devices found for user: {}", userId);
                return CommandResponse.success(null, "Không tìm thấy thiết bị nào để xóa");
            }
        } catch (Exception e) {
            log.error("Error deleting user devices for user {}: {}", userId, e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi xóa thiết bị user: " + e.getMessage());
        }
    }

    @Override
    public List<UserDeviceRes> getUserDevices(String userId) {
        try {
            return portalClient.getUserDevices(userId);
        } catch (Exception e) {
            log.error("Error getting user devices for user {}: {}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // =====================================================
    // HYBRID APPROACH HELPER METHODS
    // =====================================================

    /**
     * PHASE 1: Xóa dữ liệu local trong cùng database (ACID guaranteed)
     */
    private void deleteUserDataInSameDatabase(String userId, List<String> deletionLog) {
        try {
            // Xóa tất cả OTP của user (local database)
            long otpCount = otpRepository.countByUserId(userId);
            if (otpCount > 0) {
                otpRepository.deleteByUserId(userId);
                deletionLog.add(String.format("✅ Đã xóa %d OTP (local DB)", otpCount));
                log.info("Đã xóa {} OTP của user {} trong local database", otpCount, userId);
            } else {
                deletionLog.add("ℹ️ Không có OTP để xóa");
            }
        } catch (Exception e) {
            log.error("Lỗi khi xóa dữ liệu local cho user {}: {}", userId, e.getMessage(), e);
            throw new RuntimeException("Không thể xóa dữ liệu local: " + e.getMessage(), e);
        }
    }

    /**
     * PHASE 2: Xóa dữ liệu external services (với error handling)
     */
    private void deleteUserDataInExternalServices(String userId, List<String> deletionLog, List<Exception> errors) {
        // Portal Service - Employee
        deleteEmployeeLink(userId, deletionLog, errors);

        // Portal Service - Partner
        deletePartnerLink(userId, deletionLog, errors);

        // Portal Service - User Devices
        deleteUserDevicesData(userId, deletionLog, errors);

        // Portal Service - Salary Advance Limit
        deleteSalaryAdvanceLimitData(userId, deletionLog, errors);

        // Salary Advance Service - All Data
        deleteSalaryAdvanceData(userId, deletionLog, errors);
    }

    /**
     * PHASE 3: Xóa user khỏi Keycloak (cuối cùng vì không thể rollback)
     */
    private void deleteUserFromKeycloak(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            keycloakClient.deleteUser(userId);
            deletionLog.add("✅ Đã xóa user khỏi Keycloak");
            log.info("Đã xóa user {} khỏi Keycloak", userId);
        } catch (Exception e) {
            log.error("Lỗi khi xóa user {} khỏi Keycloak: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("❌ Lỗi: Không thể xóa user khỏi Keycloak");
            throw new RuntimeException("Không thể xóa user khỏi Keycloak: " + e.getMessage(), e);
        }
    }

    /**
     * PHASE 4: Xử lý kết quả và logging
     */
    private CommandResponse<Void> handleDeletionResult(String userId, boolean force,
            List<String> deletionLog, List<Exception> errors) {

        // Log chi tiết cho debugging
        logDeletionSummary(userId, deletionLog, errors);

        // Tạo message response
        String message = buildResponseMessage(force, deletionLog, errors);

        // Quyết định success/failure dựa trên critical errors
        boolean hasKeycloakError = errors.stream()
                .anyMatch(e -> e.getMessage().contains("Keycloak"));

        if (hasKeycloakError) {
            return CommandResponse.failure(null, message);
        } else {
            return CommandResponse.success(null, message);
        }
    }

    // =====================================================
    // EXTERNAL SERVICE DELETION METHODS
    // =====================================================

    private void deleteEmployeeLink(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            EmployeeModel employee = portalClient.getEmployeeByUserId(userId);
            if (employee != null) {
                employee.setUserId(null); // Xóa liên kết
                portalClient.updateEmployee(employee.getId(), employee);
                deletionLog.add("✅ Đã xóa liên kết với nhân viên: " + employee.getName());
                log.info("Đã xóa liên kết user {} với employee {}", userId, employee.getId());
            } else {
                deletionLog.add("ℹ️ Không có liên kết employee để xóa");
            }
        } catch (Exception e) {
            log.warn("Không thể xóa liên kết employee cho user {}: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("⚠️ Cảnh báo: Không thể xóa liên kết với nhân viên");
        }
    }

    private void deletePartnerLink(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
            if (partner != null) {
                CommandResponse<Void> partnerResult = deleteUserPartner(userId);
                if (partnerResult.isSuccess()) {
                    deletionLog.add("✅ Đã xóa liên kết user_partner với đối tác: " + partner.getName());
                } else {
                    deletionLog.add("⚠️ Cảnh báo: " + partnerResult.getMessage());
                }
            } else {
                deletionLog.add("ℹ️ Không có liên kết partner để xóa");
            }
        } catch (Exception e) {
            log.warn("Không thể xóa liên kết partner cho user {}: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("⚠️ Cảnh báo: Không thể xóa liên kết với đối tác");
        }
    }

    private void deleteUserDevicesData(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            List<UserDeviceRes> devices = getUserDevices(userId);
            if (!devices.isEmpty()) {
                CommandResponse<Void> deviceResult = deleteUserDevices(userId);
                if (deviceResult.isSuccess()) {
                    deletionLog.add(String.format("✅ Đã xóa %d thiết bị của user", devices.size()));
                } else {
                    deletionLog.add("⚠️ Cảnh báo: " + deviceResult.getMessage());
                }
            } else {
                deletionLog.add("ℹ️ Không có thiết bị để xóa");
            }
        } catch (Exception e) {
            log.warn("Không thể xóa user devices cho user {}: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("⚠️ Cảnh báo: Không thể xóa thiết bị user");
        }
    }

    private void deleteSalaryAdvanceLimitData(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            boolean salaryLimitDeleted = portalClient.deleteSalaryAdvanceLimit(userId);
            if (salaryLimitDeleted) {
                deletionLog.add("✅ Đã xóa salary advance limit của employee");
            } else {
                deletionLog.add("ℹ️ Không có salary advance limit để xóa");
            }
        } catch (Exception e) {
            log.warn("Không thể xóa salary advance limit cho user {}: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("⚠️ Cảnh báo: Không thể xóa salary advance limit");
        }
    }

    private void deleteSalaryAdvanceData(String userId, List<String> deletionLog, List<Exception> errors) {
        try {
            boolean salaryAdvanceDataDeleted = salaryAdvanceClient.deleteAllEmployeeData(userId);
            if (salaryAdvanceDataDeleted) {
                deletionLog.add("✅ Đã xóa tất cả dữ liệu salary advance của employee");
            } else {
                deletionLog.add("ℹ️ Không có dữ liệu salary advance để xóa");
            }
        } catch (Exception e) {
            log.warn("Không thể xóa salary advance data cho user {}: {}", userId, e.getMessage(), e);
            errors.add(e);
            deletionLog.add("⚠️ Cảnh báo: Không thể xóa dữ liệu salary advance");
        }
    }

    // =====================================================
    // LOGGING AND RESPONSE HELPER METHODS
    // =====================================================

    private void logDeletionSummary(String userId, List<String> deletionLog, List<Exception> errors) {
        log.info("=== DELETION SUMMARY FOR USER {} ===", userId);
        log.info("Total operations: {}", deletionLog.size());
        log.info("Successful operations: {}", deletionLog.stream().filter(s -> s.startsWith("✅")).count());
        log.info("Warning operations: {}", deletionLog.stream().filter(s -> s.startsWith("⚠️")).count());
        log.info("Info operations: {}", deletionLog.stream().filter(s -> s.startsWith("ℹ️")).count());
        log.info("Total errors: {}", errors.size());

        if (!errors.isEmpty()) {
            log.warn("Errors encountered:");
            errors.forEach(e -> log.warn("- {}: {}", e.getClass().getSimpleName(), e.getMessage()));
        }

        log.info("Deletion log:");
        deletionLog.forEach(entry -> log.info("- {}", entry));
        log.info("=== END DELETION SUMMARY ===");
    }

    private String buildResponseMessage(boolean force, List<String> deletionLog, List<Exception> errors) {
        StringBuilder message = new StringBuilder();

        // Thống kê tổng quan
        long successCount = deletionLog.stream().filter(s -> s.startsWith("✅")).count();
        long warningCount = deletionLog.stream().filter(s -> s.startsWith("⚠️")).count();
        long infoCount = deletionLog.stream().filter(s -> s.startsWith("ℹ️")).count();

        if (errors.isEmpty()) {
            message.append(String.format("✅ Xóa user thành công (force=%s). ", force));
        } else {
            message.append(String.format("⚠️ Xóa user hoàn thành với cảnh báo (force=%s). ", force));
        }

        message.append(String.format("Thống kê: %d thành công, %d cảnh báo, %d thông tin. ",
                successCount, warningCount, infoCount));

        // Chi tiết các operations
        message.append("Chi tiết: ");
        message.append(String.join("; ", deletionLog));

        return message.toString();
    }

    @Override
    public UserStatisticsDto calculateUserStatistics(String userId) {
        UserStatisticsDto.UserStatisticsDtoBuilder builder = UserStatisticsDto.builder();

        try {
            // =====================================================
            // USER SERVICE STATISTICS
            // =====================================================

            // OTP count
            Long otpCount = otpRepository.countByUserId(userId);
            builder.otpCount(otpCount);

            // =====================================================
            // PORTAL SERVICE STATISTICS
            // =====================================================

            // Employee information
            try {
                EmployeeModel employee = portalClient.getEmployeeByUserId(userId);
                if (employee != null) {
                    builder.hasEmployee(true);
                    builder.employeeName(employee.getName());
                } else {
                    builder.hasEmployee(false);
                }
            } catch (Exception e) {
                log.warn("Cannot get employee info for user {}: {}", userId, e.getMessage());
                builder.hasEmployee(false);
            }

            // Partner information
            try {
                PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
                if (partner != null) {
                    builder.hasPartner(true);
                    builder.partnerName(partner.getName());
                } else {
                    builder.hasPartner(false);
                }
            } catch (Exception e) {
                log.warn("Cannot get partner info for user {}: {}", userId, e.getMessage());
                builder.hasPartner(false);
            }

            // Device count
            try {
                List<UserDeviceRes> devices = getUserDevices(userId);
                builder.deviceCount((long) devices.size());
            } catch (Exception e) {
                log.warn("Cannot get device count for user {}: {}", userId, e.getMessage());
                builder.deviceCount(0L);
            }

            // Salary advance limit count (placeholder - cần implement API)
            builder.salaryAdvanceLimitCount(0L); // TODO: Implement API to get count

            // =====================================================
            // SALARY ADVANCE SERVICE STATISTICS
            // =====================================================

            try {
                java.util.Map<String, Long> salaryAdvanceData = salaryAdvanceClient.getEmployeeDataCount(userId);

                Long transactionCount = salaryAdvanceData.getOrDefault("transaction", 0L) +
                        salaryAdvanceData.getOrDefault("transaction_aud", 0L);
                Long requestCount = salaryAdvanceData.getOrDefault("salary_advance_request", 0L) +
                        salaryAdvanceData.getOrDefault("salary_advance_request_aud", 0L);

                builder.transactionCount(transactionCount);
                builder.salaryAdvanceRequestCount(requestCount);

            } catch (Exception e) {
                log.warn("Cannot get salary advance data for user {}: {}", userId, e.getMessage());
                builder.transactionCount(0L);
                builder.salaryAdvanceRequestCount(0L);
            }

            // Build and calculate
            UserStatisticsDto statistics = builder.build();
            statistics.calculateTotals();
            statistics.calculateRiskLevel();

            return statistics;

        } catch (Exception e) {
            log.error("Error calculating statistics for user {}: {}", userId, e.getMessage(), e);

            // Return default statistics in case of error
            UserStatisticsDto defaultStats = UserStatisticsDto.builder()
                    .otpCount(0L)
                    .hasEmployee(false)
                    .hasPartner(false)
                    .deviceCount(0L)
                    .salaryAdvanceLimitCount(0L)
                    .transactionCount(0L)
                    .salaryAdvanceRequestCount(0L)
                    .build();

            defaultStats.calculateTotals();
            defaultStats.calculateRiskLevel();
            defaultStats.setSummary("ERROR: Không thể tính toán thống kê");

            return defaultStats;
        }
    }

    @Override
    public UserDetailDto getUserDetail(String userId) {
        try {
            // 1. Lấy thông tin user từ Keycloak
            UserRepresentation user = keycloakClient.findUserById(userId);
            if (user == null) {
                throw new RuntimeException("User không tồn tại: " + userId);
            }

            // 2. Lấy role mapping
            MappingsRepresentation roles = keycloakClient.findRoles(userId);

            // 3. Tính toán statistics (đây là lý do chính của API này)
            UserStatisticsDto statistics = calculateUserStatistics(userId);

            // 4. Lấy thông tin profile chi tiết
            UserDetailDto.UserProfileInfo profile = buildUserProfile(userId);

            // 5. Build response
            return UserDetailDto.builder()
                    .user(user)
                    .roles(roles)
                    .statistics(statistics)
                    .profile(profile)
                    .build();

        } catch (Exception e) {
            log.error("Error getting user detail for user {}: {}", userId, e.getMessage(), e);
            throw new RuntimeException("Không thể lấy thông tin chi tiết user: " + e.getMessage(), e);
        }
    }

    /**
     * Build thông tin profile chi tiết từ Portal service
     */
    private UserDetailDto.UserProfileInfo buildUserProfile(String userId) {
        UserDetailDto.UserProfileInfo.UserProfileInfoBuilder profileBuilder = UserDetailDto.UserProfileInfo.builder();

        // Employee information
        try {
            EmployeeModel employee = portalClient.getEmployeeByUserId(userId);
            if (employee != null) {
                UserDetailDto.EmployeeInfo employeeInfo = UserDetailDto.EmployeeInfo.builder()
                        .id(employee.getId())
                        .name(employee.getName())
                        .email(employee.getEmail())
                        .code(employee.getCode())
                        .partnerId(employee.getPartnerId())
                        .build();

                // Lấy tên partner nếu có
                if (employee.getPartnerId() != null) {
                    try {
                        PartnerModel partner = portalClient.getPartnerById(employee.getPartnerId());
                        if (partner != null) {
                            employeeInfo.setPartnerName(partner.getName());
                        }
                    } catch (Exception e) {
                        log.warn("Cannot get partner name for employee {}: {}", employee.getId(), e.getMessage());
                    }
                }

                profileBuilder.employee(employeeInfo);
            }
        } catch (Exception e) {
            log.warn("Cannot get employee info for user {}: {}", userId, e.getMessage());
        }

        // Partner information
        try {
            PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
            if (partner != null) {
                UserDetailDto.PartnerInfo partnerInfo = UserDetailDto.PartnerInfo.builder()
                        .id(partner.getId())
                        .name(partner.getName())
                        .code(partner.getTaxCode()) // Sử dụng taxCode thay vì code
                        .email(partner.getEmail())
                        .phone(partner.getPhoneNumber()) // Sử dụng phoneNumber
                        .build();
                profileBuilder.partner(partnerInfo);
            }
        } catch (Exception e) {
            log.warn("Cannot get partner info for user {}: {}", userId, e.getMessage());
        }

        // Device information - simplified vì UserDeviceRes chỉ có 3 fields
        try {
            List<UserDeviceRes> devices = getUserDevices(userId);
            List<UserDetailDto.DeviceInfo> deviceInfos = devices.stream()
                    .map(device -> UserDetailDto.DeviceInfo.builder()
                            .id(null) // UserDeviceRes không có id
                            .deviceId(device.getDeviceToken()) // Sử dụng deviceToken làm deviceId
                            .deviceName("Unknown") // UserDeviceRes không có deviceName
                            .platform(device.getDeviceType()) // Sử dụng deviceType làm platform
                            .status("ACTIVE") // Mặc định ACTIVE
                            .createdAt(null) // UserDeviceRes không có createdAt
                            .lastActiveAt(null) // UserDeviceRes không có lastActiveAt
                            .build())
                    .collect(java.util.stream.Collectors.toList());
            profileBuilder.devices(deviceInfos);
        } catch (Exception e) {
            log.warn("Cannot get device info for user {}: {}", userId, e.getMessage());
            profileBuilder.devices(java.util.Collections.emptyList());
        }

        return profileBuilder.build();
    }
}
