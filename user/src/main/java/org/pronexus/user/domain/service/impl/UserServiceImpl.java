package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.constants.ApiMessage;
import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.exception.base.ResourceExistedException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.utils.StringUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.MappingsRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.keycloak.representations.idm.UserSessionRepresentation;
import org.pronexus.user.app.dtos.*;
import org.pronexus.user.app.dtos.otp.GenerateOtpRequestDto;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpRequestDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpResponseDto;
import com.salaryadvance.commonlibrary.enums.RoleType;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpType;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;
import org.pronexus.user.domain.feign.adapter.PortalClient;
import org.pronexus.user.domain.mapper.UserMapper;
import org.pronexus.user.domain.model.external.EmployeeModel;
import org.pronexus.user.domain.model.external.PartnerModel;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.pronexus.user.domain.service.core.RoleService;
import org.pronexus.user.domain.service.core.UserService;
import org.pronexus.user.domain.service.core.OtpService;
import org.pronexus.user.domain.repository.OtpRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final KeycloakClient keycloakClient;
    private final PortalClient portalClient;
    private final UserMapper userMapper;
    private final RoleService roleService;
    private final OtpService otpService;
    private final OtpRepository otpRepository;

    @Override
    public Page<UserWithRoleMappingModel> getUsers(Pageable pageable, String username, boolean withRoles) {
        return keycloakClient.getUsers(pageable, username).map(u -> {
            UserWithRoleMappingModel userWithRole = new UserWithRoleMappingModel();
            userWithRole.setUser(u);
            if (withRoles) {
                MappingsRepresentation roleMapping = keycloakClient.findRoles(u.getId());
                userWithRole.setRoles(roleMapping);
            }
            return userWithRole;
        });
    }

    @Override
    public Page<UserRepresentation> getUsersOnly(Pageable pageable, String username) {
        return keycloakClient.getUsers(pageable, username);
    }

    @Override
    @Transactional
    public CommandResponse<Void> registerUser(RegisterUserCommandDto command) {
        register(command);

        // Thông báo cho người dùng cần xác thực OTP
        log.info("User is created and needs OTP verification: {}", command.getUsername());
        return CommandResponse.success(null, "Tài khoản đã được tạo. Vui lòng xác thực OTP để kích hoạt tài khoản.");
    }

    private void register(RegisterUserCommandDto command) {
        boolean exists = keycloakClient.usernameExisted(command.getUsername());
        if (exists)
            throw new ResourceExistedException(String.format("Username %s already exists!", command.getUsername()),
                    null);
        UserRepresentation registrationData = userMapper.collectRegistrationData(command);
        keycloakClient.createUser(registrationData);
        log.info("User is created: {}", command.getUsername());
    }

    @Override
    @Transactional
    public CommandResponse<UserRepresentation> registerPartnerUser(RegisterUserCommandDto command) {
        register(command);
        UserRepresentation registeredUser = keycloakClient.findUserByUsername(command.getUsername());
        if (command.getRoles() != null && !command.getRoles().isEmpty()) {
            assignRole(command.getRoles(), registeredUser.getId());
        }

        // Thông báo cho người dùng cần xác thực OTP
        log.info("Partner user is created and needs OTP verification: {}", command.getUsername());
        return CommandResponse.success(registeredUser,
                "Tài khoản đối tác đã được tạo. Vui lòng xác thực OTP để kích hoạt tài khoản.");
    }

    @Override
    public CommandResponse<AccessTokenResponse> login(LoginCommandDto command) {
        // check user exists
        boolean exists = keycloakClient.usernameExisted(command.getUsername());
        if (!exists)
            throw new BadRequestException(
                    "Số điện thoại này chưa đăng ký tài khoản trên hệ thống, vui lòng kiểm tra lại.");

        // Kiểm tra tài khoản đã được xác thực chưa
        UserRepresentation user = keycloakClient.findUserByUsername(command.getUsername());
        if (!user.isEnabled()) {
            Map<String, List<String>> attributes = user.getAttributes();
            if (attributes != null && attributes.containsKey("registration_complete")) {
                List<String> registrationComplete = attributes.get("registration_complete");
                if (registrationComplete != null && !registrationComplete.isEmpty()
                        && "false".equals(registrationComplete.get(0))) {
                    throw new BadRequestException(
                            "Tài khoản chưa được xác thực. Vui lòng xác thực OTP để kích hoạt tài khoản.");
                }
            }
        }

        try {
            AccessTokenResponse accessToken = keycloakClient.getToken(command.getUsername(), command.getPassword());
            log.info("User {} is logged in!", command.getUsername());
            return CommandResponse.success(accessToken, ApiMessage.loggedIn());
        } catch (Exception e) {
            log.error("Login failed for user {}: {}", command.getUsername(), e.getMessage());
            // Return specific error message for incorrect password
            throw new BadRequestException("Mật khẩu bạn nhập không đúng, vui lòng kiểm tra và nhập lại.", e);
        }
    }

    @Override
    public CommandResponse<AccessTokenResponse> refreshToken(RefreshTokenCommand command) {
        AccessTokenResponse accessToken = keycloakClient.refreshToken(command.getRefreshToken());
        log.info("Refresh token success fully");
        return CommandResponse.success(accessToken, "Refresh token successfully");
    }

    @Override
    public UserProfileModelDto getUserProfile(boolean withRole) {
        String userId = TokenUtils.getUserId();
        UserRepresentation user = keycloakClient.findUserById(userId);
        EmployeeModel profile = portalClient.getEmployeeByUserId(userId);
        List<UserSessionRepresentation> sessions = keycloakClient.getUserSessions(userId);

        UserProfileModelDto returnData = userMapper.toUserProfileModel(user, profile);
        returnData.setAvatar(getAttributeValue(user, "avatar"));
        returnData.setEmail(user.getEmail());

        // Xử lý partner dựa vào role
        if (roleService.hasRole(RoleType.PORTAL_ADMIN)) {
            PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
            if (partner != null) {
                returnData.setPartnerId(partner.getId());
                returnData.setPartner(partner);
            }
        } else if (profile != null) {
            Long partnerId = profile.getPartnerId();
            if (partnerId != null) {
                returnData.setPartner(portalClient.getPartnerById(partnerId));
            }
        }

        // Xử lý role mapping và sessions
        if (withRole) {
            returnData.setRoleMapping(keycloakClient.findRoles(userId));
        }
        returnData.setUserSessions(sessions);

        return returnData;
    }

    @Override
    @Transactional
    public CommandResponse<Void> updateUserProfile(UpdateUserProfileCommandDto command) {
        String userId = TokenUtils.getUserId();
        UserRepresentation user = keycloakClient.findUserById(userId);

        if (command.getPartnerId() != null && command.getCode() != null) {
            // Case 1: User trying to map with employee
            String query = "partnerId:eq:" + command.getPartnerId() + "&" + "code:eq:" + command.getCode();
            EmployeeModel employee = portalClient.queryEmployee(query);
            if (Objects.nonNull(employee)) {
                if (!StringUtils.isNullOrEmpty(employee.getUserId()) && !employee.getUserId().equals(userId)) {
                    return CommandResponse.failure(null, "Nhân sự đã được gán cho user khác");
                }
                user.setFirstName(employee.getName());
                user.setLastName(employee.getName());
                user.setEmail(employee.getEmail());

                // Update user attributes and save to Keycloak
                updateUserAndSave(user, command.getAvatar(), command.getEmail());

                // Update employee mapping
                employee.setUserId(user.getId());
                employee.setAvatar(command.getAvatar());
                portalClient.updateEmployee(employee.getId(), employee);
                log.info("User {} has profile updated with employee mapping and avatar!", userId);
                return CommandResponse.success(null, ApiMessage.updated("User profile"));
            } else {
                log.error("Employee with partner id {} and code {} does not exist!", command.getPartnerId(),
                        command.getCode());
                return CommandResponse.failure(null, ApiMessage.notFound("Employee"));
            }
        } else {
            // Case 2: User only updating Keycloak attributes
            updateUserAndSave(user, command.getAvatar(), command.getEmail());
            log.info("User {} has profile updated in Keycloak only!", userId);
            return CommandResponse.success(null, ApiMessage.updated("User profile"));
        }
    }

    @Override
    public CommandResponse<Void> changePassword(ChangePasswordCommandDto command) {
        try {
            validateCurrentPassword(command.getOldPassword());
            validateNewPassword(command);
            String userId = TokenUtils.getUserId();
            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(command.getNewPassword());
            credential.setTemporary(false);
            keycloakClient.resetPassword(userId, credential);
            return CommandResponse.success(null, ApiMessage.updated("Password"));
        } catch (Exception e) {
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> assignRole(String userId, List<RoleRepresentation> role) {
        try {
            keycloakClient.assignRole(userId, role);
            return CommandResponse.success(null, "Assigned role successfully");
        } catch (Exception e) {
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    private void validateCurrentPassword(String currentPassword) {
        try {
            keycloakClient.getToken(TokenUtils.getUsername(), currentPassword);
        } catch (Exception e) {
            log.error("Old password not correct!, {}", e.getMessage());
            throw new DataValidationException("Old password not correct", Map.of("details", e.getMessage()));
        }
    }

    private void validateNewPassword(ChangePasswordCommandDto command) {
        if (!command.getNewPassword().equals(command.getConfirmPassword())) {
            log.error("Confirmation password not match!");
            throw new DataValidationException("Confirmation password not match", null);
        }
    }

    private void assignRole(List<String> roles, String userId) {
        List<RoleRepresentation> rolesRepresentation = roles.stream().map(keycloakClient::getRole).toList();
        keycloakClient.assignRole(userId, rolesRepresentation);
    }

    @Override
    public CommandResponse<Void> logout() {
        try {
            keycloakClient.logout();
            return CommandResponse.success(null, "Logged out successfully");
        } catch (Exception e) {
            log.error("Failed to logout: {}", e.getMessage());
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    @Override
    public boolean usernameExisted(String username) {
        return keycloakClient.usernameExisted(username);
    }

    @Override
    public CommandResponse<Void> forgotPassword(ForgotPasswordCommandDto command) {
        try {
            // Kiểm tra số điện thoại tồn tại
            if (!keycloakClient.usernameExisted(command.getPhoneNumber())) {
                return CommandResponse.failure(null, "Số điện thoại chưa được đăng ký");
            }

            // Tạo yêu cầu gửi OTP
            GenerateOtpRequestDto otpRequest = GenerateOtpRequestDto.builder()
                    .type(OtpType.PASSWORD_RESET)
                    .contactInfo(command.getPhoneNumber())
                    .deliveryMethod(OtpDeliveryMethod.ZNS)
                    .build();

            // Gửi OTP thông qua OTP service
            CommandResponse<GenerateOtpResponseDto> otpResponse = otpService.generateOtp(otpRequest);

            if (!otpResponse.isSuccess()) {
                return CommandResponse.failure(null, "Không thể gửi OTP: " + otpResponse.getMessage());
            }

            return CommandResponse.success(null, "Mã OTP đã được gửi đến số điện thoại của bạn");
        } catch (Exception e) {
            log.error("Lỗi khi xử lý yêu cầu quên mật khẩu", e);
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> verifyOTPAndResetPassword(VerifyOTPCommandDto command) {
        try {
            // Kiểm tra số điện thoại tồn tại
            if (!keycloakClient.usernameExisted(command.getPhoneNumber())) {
                return CommandResponse.failure(null, "Số điện thoại không tồn tại");
            }

            // Xác thực OTP
            VerifyOtpRequestDto verifyRequest = VerifyOtpRequestDto.builder()
                    .type(OtpType.PASSWORD_RESET)
                    .contactInfo(command.getPhoneNumber())
                    .code(command.getOtp())
                    .build();

            CommandResponse<VerifyOtpResponseDto> verifyResponse = otpService.verifyOtp(verifyRequest, true);

            if (!verifyResponse.isSuccess()) {
                return CommandResponse.failure(null, "OTP không hợp lệ hoặc đã hết hạn");
            }

            // Kiểm tra mật khẩu mới
            if (!command.getNewPassword().equals(command.getConfirmPassword())) {
                return CommandResponse.failure(null, "Mật khẩu xác nhận không khớp");
            }

            if (!isValidPassword(command.getNewPassword())) {
                return CommandResponse.failure(null, "Mật khẩu không đáp ứng yêu cầu bảo mật");
            }

            // Lấy user từ Keycloak và đổi mật khẩu
            UserRepresentation user = keycloakClient.findUserByUsername(command.getPhoneNumber());
            CredentialRepresentation credential = new CredentialRepresentation();
            credential.setType(CredentialRepresentation.PASSWORD);
            credential.setValue(command.getNewPassword());
            credential.setTemporary(false);
            keycloakClient.resetPassword(user.getId(), credential);

            return CommandResponse.success(null, "Đặt lại mật khẩu thành công");
        } catch (Exception e) {
            log.error("Lỗi khi xác thực OTP và đặt lại mật khẩu", e);
            return CommandResponse.failure(null, e.getMessage());
        }
    }

    private boolean isValidPassword(String password) {
        // Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự
        // đặc biệt
        String pattern = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$";
        return password.matches(pattern);
    }

    /**
     * Updates user attributes and email in the UserRepresentation object
     *
     * @param user   UserRepresentation object to update
     * @param avatar Avatar URL to set (can be null or empty)
     * @param email  Email to set (can be null or empty)
     */
    private void updateUserAttributes(UserRepresentation user, String avatar, String email) {
        // Update avatar attribute if provided
        if (!StringUtils.isNullOrEmpty(avatar)) {
            Map<String, List<String>> attributes = user.getAttributes();
            if (attributes == null) {
                attributes = new HashMap<>();
                user.setAttributes(attributes);
            }
            attributes.put("avatar", List.of(avatar));
        }

        // Update email if provided
        if (!StringUtils.isNullOrEmpty(email)) {
            user.setEmail(email);
        }
    }

    /**
     * Updates user attributes and saves to Keycloak
     * 
     * @param user   UserRepresentation object to update
     * @param avatar Avatar URL to set (can be null or empty)
     * @param email  Email to set (can be null or empty)
     */
    private void updateUserAndSave(UserRepresentation user, String avatar, String email) {
        updateUserAttributes(user, avatar, email);
        keycloakClient.updateUser(user.getId(), user);
    }

    /**
     * Retrieves the first value of a specific attribute from user's attributes map
     * 
     * @param user          UserRepresentation object containing attributes
     * @param attributeName Name of the attribute to retrieve
     * @return The first value of the attribute or empty string if not found or
     *         exception occurs
     */
    private String getAttributeValue(UserRepresentation user, String attributeName) {
        try {
            if (user.getAttributes() == null) {
                return "";
            }

            Map<String, List<String>> attributes = user.getAttributes();
            List<String> valueList = attributes.get(attributeName);

            if (valueList != null && !valueList.isEmpty()) {
                return valueList.get(0);
            }

            return "";
        } catch (Exception e) {
            log.debug("Error getting attribute {} for user: {}", attributeName, e.getMessage());
            return "";
        }
    }

    @Override
    public UserDeletionWarningDto checkUserDeletionWarnings(String userId) {
        List<String> warnings = new ArrayList<>();
        List<UserDeletionWarningDto.RelatedDataInfo> relatedData = new ArrayList<>();
        boolean canDelete = true;

        try {
            // Kiểm tra user tồn tại trong Keycloak
            UserRepresentation user = keycloakClient.findUserById(userId);
            if (user == null) {
                warnings.add("User không tồn tại trong hệ thống Keycloak");
                return new UserDeletionWarningDto(false, warnings, relatedData);
            }

            // 1. Kiểm tra OTP
            long otpCount = otpRepository.countByUserId(userId);
            if (otpCount > 0) {
                relatedData.add(new UserDeletionWarningDto.RelatedDataInfo(
                        "user_schema.otp", "Mã OTP của user", otpCount));
                warnings.add(String.format("User có %d mã OTP trong hệ thống", otpCount));
            }

            // 2. Kiểm tra Employee thông qua Portal service
            EmployeeModel employee = portalClient.getEmployeeByUserId(userId);
            if (employee != null) {
                relatedData.add(new UserDeletionWarningDto.RelatedDataInfo(
                        "portal.employee", "Thông tin nhân viên", 1L));
                warnings.add("User đã được liên kết với nhân viên: " + employee.getName());
                canDelete = false; // Không cho phép xóa nếu có employee
            }

            // 3. Kiểm tra Partner thông qua Portal service
            PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
            if (partner != null) {
                relatedData.add(new UserDeletionWarningDto.RelatedDataInfo(
                        "portal.user_partner", "Liên kết với đối tác", 1L));
                warnings.add("User đã được liên kết với đối tác: " + partner.getName());
                canDelete = false; // Không cho phép xóa nếu có partner
            }

            // 4. Kiểm tra User Device
            List<UserDeviceRes> devices = getUserDevices(userId);
            if (!devices.isEmpty()) {
                relatedData.add(new UserDeletionWarningDto.RelatedDataInfo(
                        "portal.user_device", "Thiết bị của user", (long) devices.size()));
                warnings.add(String.format("User có %d thiết bị đã đăng ký", devices.size()));
            }

            // Thêm cảnh báo chung
            if (!warnings.isEmpty()) {
                warnings.add(0, "CẢNH BÁO: Xóa user sẽ ảnh hưởng đến dữ liệu liên quan:");
            }

            if (canDelete && !relatedData.isEmpty()) {
                warnings.add("Dữ liệu liên quan sẽ được xóa cùng với user");
            }

        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra dữ liệu liên quan của user {}: {}", userId, e.getMessage());
            warnings.add("Lỗi khi kiểm tra dữ liệu liên quan: " + e.getMessage());
            canDelete = false;
        }

        return new UserDeletionWarningDto(canDelete, warnings, relatedData);
    }

    @Override
    @Transactional
    public CommandResponse<Void> deleteUser(String userId, boolean force) {
        try {
            // Kiểm tra cảnh báo trước khi xóa
            UserDeletionWarningDto warnings = checkUserDeletionWarnings(userId);

            if (!force && !warnings.isCanDelete()) {
                return CommandResponse.failure(null,
                        "Không thể xóa user: " + String.join("; ", warnings.getWarnings()));
            }

            // Kiểm tra user tồn tại
            UserRepresentation user = keycloakClient.findUserById(userId);
            if (user == null) {
                return CommandResponse.failure(null, "User không tồn tại");
            }

            // Xóa dữ liệu liên quan trước
            List<String> deletionLog = new ArrayList<>();

            // 1. Xóa tất cả OTP của user
            long otpCount = otpRepository.countByUserId(userId);
            if (otpCount > 0) {
                otpRepository.deleteByUserId(userId);
                deletionLog.add(String.format("Đã xóa %d OTP", otpCount));
                log.info("Đã xóa {} OTP của user {}", otpCount, userId);
            }

            // 2. Nếu force = true, xử lý dữ liệu liên quan
            if (force) {
                // Xử lý Employee - chỉ xóa liên kết user_id, không xóa employee
                EmployeeModel employee = portalClient.getEmployeeByUserId(userId);
                if (employee != null) {
                    employee.setUserId(null); // Xóa liên kết
                    try {
                        portalClient.updateEmployee(employee.getId(), employee);
                        deletionLog.add("Đã xóa liên kết với nhân viên: " + employee.getName());
                        log.info("Đã xóa liên kết user {} với employee {}", userId, employee.getId());
                    } catch (Exception e) {
                        log.warn("Không thể xóa liên kết với employee {}: {}", employee.getId(), e.getMessage());
                        deletionLog.add("Cảnh báo: Không thể xóa liên kết với nhân viên");
                    }
                }

                // Xử lý Partner - xóa liên kết trong user_partner nhưng giữ partner
                PartnerModel partner = portalClient.getPartnerByKeyCloakId(userId);
                if (partner != null) {
                    CommandResponse<Void> partnerResult = deleteUserPartner(userId);
                    if (partnerResult.isSuccess()) {
                        deletionLog.add("Đã xóa liên kết user_partner với đối tác: " + partner.getName());
                    } else {
                        deletionLog.add("Cảnh báo: " + partnerResult.getMessage());
                    }
                }

                // Xử lý User Device - xóa tất cả thiết bị của user
                List<UserDeviceRes> devices = getUserDevices(userId);
                if (!devices.isEmpty()) {
                    CommandResponse<Void> deviceResult = deleteUserDevices(userId);
                    if (deviceResult.isSuccess()) {
                        deletionLog.add(String.format("Đã xóa %d thiết bị của user", devices.size()));
                    } else {
                        deletionLog.add("Cảnh báo: " + deviceResult.getMessage());
                    }
                }
            }

            // 3. Xóa user khỏi Keycloak
            keycloakClient.deleteUser(userId);
            deletionLog.add("Đã xóa user khỏi Keycloak");
            log.info("Đã xóa user {} khỏi Keycloak", userId);

            String message = force ? "Xóa user thành công (force mode): " + String.join("; ", deletionLog)
                    : "Xóa user thành công";

            return CommandResponse.success(null, message);

        } catch (Exception e) {
            log.error("Lỗi khi xóa user {}: {}", userId, e.getMessage());
            return CommandResponse.failure(null, "Lỗi khi xóa user: " + e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> deleteUserPartner(String userId) {
        try {
            // Gọi Portal service để xóa user_partner
            boolean deleted = portalClient.deleteUserPartner(userId);
            if (deleted) {
                log.info("Successfully deleted user_partner for user: {}", userId);
                return CommandResponse.success(null, "Đã xóa liên kết user-partner thành công");
            } else {
                log.warn("No user_partner found for user: {}", userId);
                return CommandResponse.success(null, "Không tìm thấy liên kết user-partner");
            }
        } catch (Exception e) {
            log.error("Error deleting user_partner for user {}: {}", userId, e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi xóa liên kết user-partner: " + e.getMessage());
        }
    }

    @Override
    public CommandResponse<Void> deleteUserDevices(String userId) {
        try {
            // Gọi Portal service để xóa user devices
            boolean deleted = portalClient.deleteUserDevices(userId);
            if (deleted) {
                log.info("Successfully deleted user devices for user: {}", userId);
                return CommandResponse.success(null, "Đã xóa thiết bị của user thành công");
            } else {
                log.warn("No user devices found for user: {}", userId);
                return CommandResponse.success(null, "Không tìm thấy thiết bị nào để xóa");
            }
        } catch (Exception e) {
            log.error("Error deleting user devices for user {}: {}", userId, e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi xóa thiết bị user: " + e.getMessage());
        }
    }

    @Override
    public List<UserDeviceRes> getUserDevices(String userId) {
        try {
            return portalClient.getUserDevices(userId);
        } catch (Exception e) {
            log.error("Error getting user devices for user {}: {}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
