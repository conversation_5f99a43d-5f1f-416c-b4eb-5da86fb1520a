package org.pronexus.user.domain.feign.adapter;

import com.salaryadvance.commonlibrary.exception.base.ExternalServiceException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.feign.PortalFeignClient;
import org.pronexus.user.domain.model.external.EmployeeModel;
import org.pronexus.user.domain.model.external.PartnerModel;
import org.pronexus.user.domain.model.external.UserDeviceRes;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class PortalClient {

    private final KeycloakClient keycloakClient;
    private final PortalFeignClient portalFeignClient;

    public EmployeeModel queryEmployee(String query) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<EmployeeModel> response = portalFeignClient.queryEmployee(token, query);
            return response.getData();
        } catch (FeignException e) {
            log.error("[QUERY EMPLOYEE] Employee not found with query {}", query, e);
            return null;
        }
    }

    public EmployeeModel getEmployeeByPhoneNumber(String phoneNumber) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            String query = "phoneNumber:eq:" + phoneNumber;
            Response<EmployeeModel> response = portalFeignClient.queryEmployee(token, query);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH EMPLOYEE] Employee not found with phone number {}", phoneNumber, e);
            return null;
        }
    }

    public EmployeeModel getEmployeeByUserId(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            String query = "userId:eq:" + userId;
            Response<EmployeeModel> response = portalFeignClient.queryEmployee(token, query);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH EMPLOYEE] Cannot get employee of id {}: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    public PartnerModel getPartnerById(Long id) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<PartnerModel> response = portalFeignClient.getPartner(token, id);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH PARTNER] Cannot get partner of id {}: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * get partner by user id of keycloak
     * 
     * @param id
     * @return
     */
    public PartnerModel getPartnerByKeyCloakId(String id) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<PartnerModel> response = portalFeignClient.getPartnerByKeycloakId(token, id);
            return response.getData();
        } catch (FeignException e) {
            log.error("[SEARCH PARTNER] Cannot get partner of keycloak user id {}: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Xóa liên kết user-partner
     * 
     * @param userId ID của user
     * @return true nếu thành công
     */
    public boolean deleteUserPartner(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = portalFeignClient.deleteUserPartner(token, userId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE USER_PARTNER] Cannot delete user_partner for user {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Xóa tất cả thiết bị của user
     * 
     * @param userId ID của user
     * @return true nếu thành công
     */
    public boolean deleteUserDevices(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = portalFeignClient.deleteUserDevices(token, userId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE USER_DEVICES] Cannot delete user devices for user {}: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Lấy danh sách thiết bị của user
     * 
     * @param userId ID của user
     * @return danh sách thiết bị
     */
    public List<UserDeviceRes> getUserDevices(String userId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<List<UserDeviceRes>> response = portalFeignClient.getUserDevices(token, userId);
            return response.getData() != null ? response.getData() : Collections.emptyList();
        } catch (FeignException e) {
            log.error("[GET USER_DEVICES] Cannot get user devices for user {}: {}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public void updateEmployee(Long employeeId, EmployeeModel employeeModel) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            CommandResponse<EmployeeModel> response = portalFeignClient.updateEmployee(token, employeeId, employeeModel)
                    .getData();
            if (!response.isSuccess()) {
                log.error("[UPDATE EMPLOYEE] Failed to sync employee with user {}, message: {}", employeeId,
                        response.getMessage());
            }
        } catch (FeignException e) {
            log.error("[UPDATE EMPLOYEE] Failed to sync employee with user, message: {}", e.getMessage(), e);
            throw new ExternalServiceException("Cannot update employee due to server error",
                    Map.of("details", e.getMessage()));
        }
    }
}
