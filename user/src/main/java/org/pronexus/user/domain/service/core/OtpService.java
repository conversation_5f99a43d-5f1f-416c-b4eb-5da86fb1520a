package org.pronexus.user.domain.service.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.pronexus.user.app.dtos.otp.GenerateOtpRequestDto;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpRequestDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpResponseDto;
import org.pronexus.user.domain.entity.OtpType;

/**
 * Interface định nghĩa các phương thức của OtpService
 */
public interface OtpService {

    /**
     * Tạo mã OTP mới
     *
     * @param requestDto Thông tin yêu cầu tạo OTP
     * @return Thông tin OTP đã tạo
     */
    CommandResponse<GenerateOtpResponseDto> generateOtp(GenerateOtpRequestDto requestDto);

    /**
     * Tạo mã OTP mới với userId được chỉ định (dùng cho admin hoặc hệ thống)
     *
     * @param userId ID của người dùng
     * @param requestDto Thông tin yêu cầu tạo OTP
     * @return Thông tin OTP đã tạo
     */
    CommandResponse<GenerateOtpResponseDto> generateOtpForUser(String userId, GenerateOtpRequestDto requestDto);

    /**
     * Xác thực mã OTP
     *
     * @param requestDto Thông tin yêu cầu xác thực OTP
     * @param updateStatus Có cập nhật trạng thái OTP sau khi xác thực hay không
     * @return Kết quả xác thực
     */
    CommandResponse<VerifyOtpResponseDto> verifyOtp(VerifyOtpRequestDto requestDto, boolean updateStatus);

    /**
     * Xác thực mã OTP với userId được chỉ định (dùng cho admin hoặc hệ thống)
     *
     * @param userId ID của người dùng
     * @param requestDto Thông tin yêu cầu xác thực OTP
     * @return Kết quả xác thực
     */
    CommandResponse<VerifyOtpResponseDto> verifyOtpForUser(String userId, VerifyOtpRequestDto requestDto);

    /**
     * Xác thực mã OTP với userId được chỉ định (dùng cho admin hoặc hệ thống)
     *
     * @param userId ID của người dùng
     * @param requestDto Thông tin yêu cầu xác thực OTP
     * @param updateStatus Có cập nhật trạng thái OTP sau khi xác thực hay không
     * @return Kết quả xác thực
     */
    CommandResponse<VerifyOtpResponseDto> verifyOtpForUser(String userId, VerifyOtpRequestDto requestDto, boolean updateStatus);


    /**
     * Vô hiệu hóa tất cả OTP của một người dùng theo loại
     *
     * @param userId ID của người dùng
     * @param type Loại OTP
     * @return Kết quả vô hiệu hóa
     */
    CommandResponse<Void> invalidateAllOtpsByUserAndType(String userId, OtpType type);

    /**
     * Vô hiệu hóa OTP theo ID
     *
     * @param otpId ID của OTP
     * @return Kết quả vô hiệu hóa
     */
    CommandResponse<Void> invalidateOtp(Long otpId);

    /**
     * Lấy mã OTP mới nhất theo số điện thoại và loại OTP (chỉ dùng cho mục đích test)
     *
     * @param contactInfo Số điện thoại
     * @param type Loại OTP
     * @return Mã OTP
     */
    CommandResponse<String> getLatestOtpForTesting(String contactInfo, OtpType type);
}
