package org.pronexus.user.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.feign.adapter.ZnsClient;
import org.pronexus.user.domain.feign.dto.ZnsResponse;
import org.pronexus.user.domain.service.core.ZnsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Implementation của ZnsService
 * Dùng để gửi thông báo qua Zalo Notification Service (ZNS)
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZnsServiceImpl implements ZnsService {

    private final ZnsClient znsClient;

    @Value("${zns.otp.template.id}")
    private String znsOtpTemplateId;

    /**
     * <PERSON><PERSON><PERSON> thông báo OTP qua Zalo
     */
    @Override
    public ZnsResponse sendOtpNotification(String phoneNumber, String otpCode, String templateId, int otpExpiry) {
        // Sử dụng template ID mặc định nếu không được cung cấp
        if (templateId == null || templateId.isEmpty()) {
            templateId = znsOtpTemplateId;
        }

        // Gửi OTP qua ZNS và trả về response
        return znsClient.sendOtpNotification(phoneNumber, otpCode, templateId, otpExpiry);
    }

    /**
     * Gửi thông báo tùy chỉnh qua Zalo
     */
    @Override
    public ZnsResponse sendCustomNotification(String phoneNumber, String templateId, Object templateData) {
        // Chức năng này sẽ được triển khai sau khi có yêu cầu cụ thể
        throw new UnsupportedOperationException("Chức năng này chưa được triển khai");
    }
}
