# User Statistics API Documentation

## 🎯 **MỤC ĐÍCH**

API này cung cấp thông tin thống kê chi tiết về dữ liệu liên quan của từng user để admin có thể đưa ra quyết định đúng đắn trước khi xóa user.

## 📊 **API ENDPOINT**

### **GET /api/v1/user**

**Parameters:**
- `page` (optional, default: 0): Số trang
- `size` (optional, default: 10): <PERSON><PERSON><PERSON> thướ<PERSON> trang
- `username` (optional): T<PERSON><PERSON> kiếm theo username
- `role` (optional, default: true): <PERSON><PERSON> trả về role mapping hay không
- `statistics` (optional, default: true): <PERSON><PERSON> t<PERSON>h toán statistics hay không

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "user": {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "username": "0987654321",
          "firstName": "<PERSON>uyễn",
          "lastName": "Văn A",
          "email": "<EMAIL>",
          "enabled": true
        },
        "roles": {
          "realmMappings": [...],
          "clientMappings": {...}
        },
        "statistics": {
          "otpCount": 3,
          "hasEmployee": true,
          "employeeName": "Nguyễn Văn A",
          "hasPartner": false,
          "partnerName": null,
          "deviceCount": 2,
          "salaryAdvanceLimitCount": 1,
          "transactionCount": 15,
          "salaryAdvanceRequestCount": 5,
          "totalSalaryAdvanceRecords": 20,
          "totalRelatedRecords": 27,
          "riskLevel": "HIGH",
          "canSafeDelete": false,
          "summary": "HIGH: Liên kết với nhân viên Nguyễn Văn A"
        }
      }
    ],
    "pageable": {...},
    "totalElements": 100,
    "totalPages": 10
  }
}
```

## 📋 **STATISTICS OBJECT STRUCTURE**

### **User Service Data:**
- `otpCount`: Số lượng OTP của user

### **Portal Service Data:**
- `hasEmployee`: Có liên kết với employee hay không
- `employeeName`: Tên employee (nếu có)
- `hasPartner`: Có liên kết với partner hay không  
- `partnerName`: Tên partner (nếu có)
- `deviceCount`: Số lượng thiết bị đã đăng ký
- `salaryAdvanceLimitCount`: Số lượng salary advance limit

### **Salary Advance Service Data:**
- `transactionCount`: Số lượng transaction (bao gồm audit)
- `salaryAdvanceRequestCount`: Số lượng request (bao gồm audit)
- `totalSalaryAdvanceRecords`: Tổng records trong Salary Advance service

### **Summary Data:**
- `totalRelatedRecords`: Tổng số bản ghi liên quan (tất cả services)
- `riskLevel`: Mức độ rủi ro (LOW/MEDIUM/HIGH/CRITICAL)
- `canSafeDelete`: Có thể xóa an toàn với force=false hay không
- `summary`: Thông báo tóm tắt cho UI

## 🚦 **RISK LEVEL CLASSIFICATION**

### **LOW (Xanh lá):**
- 0-5 records liên quan
- Không có employee/partner
- Có thể xóa an toàn
- **UI**: Hiển thị nút "Delete" bình thường

### **MEDIUM (Vàng):**
- 6-20 records liên quan
- Có thể có employee nhưng không có partner
- Có thể xóa với cảnh báo
- **UI**: Hiển thị cảnh báo nhẹ

### **HIGH (Cam):**
- 21-50 records hoặc có employee/partner
- Cần cân nhắc kỹ trước khi xóa
- **UI**: Hiển thị cảnh báo mạnh, yêu cầu xác nhận

### **CRITICAL (Đỏ):**
- >50 records hoặc có cả employee và partner
- Rất nguy hiểm khi xóa
- **UI**: Hiển thị cảnh báo nghiêm trọng, có thể disable nút delete

## 🎨 **UI IMPLEMENTATION SUGGESTIONS**

### **User List Table:**
```html
<table>
  <thead>
    <tr>
      <th>User</th>
      <th>Employee</th>
      <th>Partner</th>
      <th>Devices</th>
      <th>Transactions</th>
      <th>Risk Level</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Nguyễn Văn A</td>
      <td>✅ Nguyễn Văn A</td>
      <td>❌</td>
      <td>2 devices</td>
      <td>15 transactions</td>
      <td>
        <span class="badge badge-danger">HIGH</span>
        <small>27 records</small>
      </td>
      <td>
        <button class="btn btn-danger" onclick="confirmDelete()">
          ⚠️ Force Delete
        </button>
      </td>
    </tr>
  </tbody>
</table>
```

### **Risk Level Badges:**
```css
.badge-low { background: #28a745; }      /* Xanh lá */
.badge-medium { background: #ffc107; }   /* Vàng */
.badge-high { background: #fd7e14; }     /* Cam */
.badge-critical { background: #dc3545; } /* Đỏ */
```

### **Confirmation Dialog:**
```javascript
function confirmDelete(userId, statistics) {
  const message = `
    Bạn có chắc muốn xóa user này?
    
    Risk Level: ${statistics.riskLevel}
    Summary: ${statistics.summary}
    
    Dữ liệu sẽ bị ảnh hưởng:
    - ${statistics.otpCount} OTP records
    - ${statistics.deviceCount} devices
    - ${statistics.transactionCount} transactions
    - ${statistics.salaryAdvanceRequestCount} salary requests
    
    Total: ${statistics.totalRelatedRecords} records
  `;
  
  if (confirm(message)) {
    deleteUser(userId, true); // force delete
  }
}
```

## ⚡ **PERFORMANCE CONSIDERATIONS**

### **Caching Strategy:**
- Cache statistics trong 5-10 phút
- Invalidate cache khi có thay đổi dữ liệu
- Sử dụng Redis hoặc in-memory cache

### **Pagination:**
- Mặc định `statistics=true` để hiển thị đầy đủ thông tin
- Có thể set `statistics=false` nếu chỉ cần list user đơn giản

### **Async Loading:**
- Load user list trước
- Load statistics sau bằng AJAX
- Hiển thị loading spinner cho statistics

## 🔧 **CONFIGURATION**

### **Application Properties:**
```properties
# Statistics calculation timeout
user.statistics.timeout=5000

# Enable/disable statistics calculation
user.statistics.enabled=true

# Cache TTL for statistics
user.statistics.cache.ttl=300
```

## 📈 **MONITORING & METRICS**

### **Key Metrics:**
- Statistics calculation time per user
- Error rate khi tính statistics
- Cache hit/miss ratio
- API response time với/không statistics

### **Alerts:**
- Statistics calculation timeout
- High error rate (>5%)
- Slow API response (>2s)

## 🚀 **FUTURE ENHANCEMENTS**

1. **Batch Statistics**: Tính statistics cho nhiều user cùng lúc
2. **Real-time Updates**: WebSocket để update statistics real-time
3. **Export Feature**: Export user list với statistics ra Excel
4. **Advanced Filtering**: Filter theo risk level, employee status, etc.
5. **Audit Trail**: Log tất cả deletion decisions với statistics context
